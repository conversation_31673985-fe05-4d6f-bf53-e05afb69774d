Always use --legacy-peer-deps when installing new npm packages.
### 0 Context
• You are coding for **Commuza**, a SaaS dashboard that matches Linear & Vercel visual DNA.  
• Brand colour `primary = #7C3AED` (Tailwind colour token).  
• UI toolkit: TailwindCSS + shadcn/ui components (Radix underneath)&#8203;:contentReference[oaicite:0]{index=0}&#8203;:contentReference[oaicite:1]{index=1}.  
• Icons: lucide-react on a 24 × 24 grid&#8203;:contentReference[oaicite:2]{index=2}.  
• Charts: Recharts inside `<ResponsiveContainer>` for fluid width&#8203;:contentReference[oaicite:3]{index=3}.  
• Dark-mode via Tailwind `class` strategy on `<html>`&#8203;:contentReference[oaicite:4]{index=4}.  
• Motion by Framer Motion; honour `prefers-reduced-motion` hook&#8203;:contentReference[oaicite:5]{index=5}.  
• Text / background contrast must comply with WCAG 2.1 AA (≥ 4.5 : 1)&#8203;:contentReference[oaicite:6]{index=6}.

### 1 General Tone
<PERSON>, typed, functional React/TS. Suggest fixes when spotting anti-patterns.

### 2 Style Conventions
Airbnb/ESLint rules; no default exports unless one React component per file; local `const`, not `let`.

### 3 Next 13/14 (App Router)
• Use `page.tsx | layout.tsx | loading.tsx | error.tsx | route.ts`.  
• Default to Server Components; mark interactive parts with `"use client"`&#8203;:contentReference[oaicite:7]{index=7}.  
• Use `generateMetadata` for SEO.  
• Import images through `next/image` with explicit `width/height` or `fill`.

### 4 Design-system specifics
• Always output Tailwind utility classes, sorted by breakpoint → positioning → box → text → colour.  
• Prefer shadcn/ui `<Button>`, `<Card>`, `<Dialog>` over raw HTML.  
• For colours, rely on Tailwind tokens (`bg-primary`, `text-primary/80`); avoid hex in markup.  
• Follow Linear’s spacious grids: 16 px base gutter, 24 px on `md`, 32 px on `lg`&#8203;:contentReference[oaicite:8]{index=8}.  
• Use Vercel/Geist font stack: `Inter, ui-sans-serif, system-ui`&#8203;:contentReference[oaicite:9]{index=9}.  
• Icons: 24 × 24, 2-px stroke, 1-px padding, never inline SVG blobs&#8203;:contentReference[oaicite:10]{index=10}.  
• Charts must live in `<ResponsiveContainer>` and inherit tailwind colours&#8203;:contentReference[oaicite:11]{index=11}.

### 5 Motion
• Entrance: spring(220, 20); hover scale 1.05; idle float on logo.  
• Wrap animations in `useReducedMotion` checks to disable transform when set&#8203;:contentReference[oaicite:12]{index=12}.

### 6 Data & API
• Fetch data in Server Actions/RSC when possible; otherwise `useSWR`.  
• Supabase queries must filter by `supabase.auth.getUser().id`.  
• Handle errors and return typed responses.

### 7 File Structure
`app/(dashboard)/...` for logged-in routes, `components/ui/` for shared primitives, `components/<feature>/` for domain widgets.

### 8 DX Rules
• No wildcard imports; no `any`; write `TODO(username): ...` comments.  
• Avoid CSS-in-JS unless explicitly requested.

### 9 Disallowed
• Class components, Moment.js, deprecated Next.js features (`_document.js` hacks), inline styles, hex colours in JSX.

### 10 Continuous Learning
If unsure, check: Tailwind docs&#8203;:contentReference[oaicite:13]{index=13}&#8203;:contentReference[oaicite:14]{index=14}, shadcn docs&#8203;:contentReference[oaicite:15]{index=15}, WCAG 2.1&#8203;:contentReference[oaicite:16]{index=16}, Framer docs&#8203;:contentReference[oaicite:17]{index=17}, Next.js docs&#8203;:contentReference[oaicite:18]{index=18}.

### IMPORTANT

Work on atomic feature at a time, ask clarifying questions if anything is fuzzy
Always make sure to respect PRD.md document