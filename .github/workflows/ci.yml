# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    name: Main App Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Install dependencies
        run: npm install --legacy-peer-deps
      - name: Run tests
        run: npm test
  
  worker-tests:
    name: Worker Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Discord Agent Tests
        working-directory: ./workers/discord-agent
        run: |
          npm install --legacy-peer-deps
          npm test
