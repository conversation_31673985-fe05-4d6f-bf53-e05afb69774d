# 📄 Commuza – Product Requirements Document (PRD)

**Version:** 1.2  
**Date:** 2025-05-17  
**Owner:** Product & Engineering Team  
**Project Type:** SaaS, AI, Multi-Channel Messaging, Moderation

---

## 1. Purpose

**Commuza** is a SaaS platform that allows companies, communities, and creators to deploy **AI-powered community managers** on channels like **Discord**, **Slack**, and **Telegram**. These agents automate engagement, moderation, and analytics using large language models (LLMs), and are configurable via a unified web dashboard.

---

## 2. Goals

| Goal                               | KPI                                    |
| ---------------------------------- | -------------------------------------- |
| Reduce manual moderation load      | ≥ 60% drop in human actions per server |
| Increase healthy engagement        | ≥ 15% MoM message growth               |
| Deliver multi-channel unification  | ≥ 80% of users connect ≥2 platforms    |
| Maximize infrastructure efficiency | Infra cost ≤ 20% of Pro plan revenue   |
| Achieve scalable revenue model     | ≥ 79% gross margin on Pro plan         |

---

## 3. Personas

| Persona           | Needs                                        | Pain Points                         |
| ----------------- | -------------------------------------------- | ----------------------------------- |
| Community Manager | Automate replies, flag abuse, schedule posts | Manual tools, platform silos        |
| Startup Founder   | Fast onboarding, brand-safe AI               | No time to build bots               |
| Developer Admin   | API access, control LLM behavior             | Closed-source bots lack flexibility |

---

## 4. Scope

### MVP Feature Set

- [x] Agent deployment wizard for Discord, Slack, Telegram
- [x] Prompt editor for tone, rules, personality
- [x] Message moderation (AI-based filtering)
- [x] Usage analytics (messages, violations, engagement)
- [x] Billing via Stripe (plans, usage metering, overages)
- [x] Role-based access and team permissions

### Out of Scope (v1)

- WhatsApp or LinkedIn integrations
- Multi-language agents
- Agent marketplace

---

## 5. Functional Requirements

### 5.1 Agent Management

- Create, update, and delete agents
- Assign channel (e.g., Discord guild or Slack workspace)
- Customize system prompt
- Control activation status

### 5.2 Multi-Channel Connectors

- **Discord**: Bot + slash commands + webhook support
- **Slack**: Events API + slash commands
- **Telegram**: Bot API + webhook

### 5.3 Message Processing

- Moderate content before LLM call
- Generate reply via OpenAI (GPT-4 or 3.5)
- Log messages and usage in Supabase
- Enforce per-user and per-channel quotas

### 5.4 Billing & Quotas

- Free, Pro, Enterprise plans
- Add-ons: extra channels, prompt modules
- Usage-based overages tracked per 1,000 messages
- Stripe checkout and customer portal

### 5.5 Dashboard

- Usage metrics: messages, AI replies, moderation events
- Agent overview: status, prompt summary, activity
- Settings: team roles, channel management, API keys
- Notification center: flagged content, failures

---

## 6. Technical Architecture

| Layer        | Stack                                         |
| ------------ | --------------------------------------------- |
| Frontend     | Next.js (App Router), TailwindCSS, shadcn/ui  |
| Backend      | Supabase (Postgres + RLS, Auth, Realtime)     |
| Runtime      | Cloudflare Worker per channel (Discord, etc.) |
| Messaging    | Durable Objects (per-agent context queue)     |
| Storage      | Cloudflare R2 (prompts, media), KV (config)   |
| AI Inference | OpenAI (chat/completions), modular design     |
| Billing      | Stripe subscriptions, usage metering          |

---

## 7. Cloudflare Workers Cost Model

| Metric                  | Per Channel Estimate          |
| ----------------------- | ----------------------------- |
| Monthly Requests        | ~100k                         |
| CPU Time                | ~3.75M ms (≈1.04 GB-sec)      |
| Cloudflare Cost         | ~$0.16 base + buffer → ~$0.30 |
| AI Inference (OpenAI)   | $5 per 100k messages          |
| Infra Cost per Pro User | ~$6 (3 channels + LLM usage)  |
| Gross Margin            | ~$28.29 (after Stripe) – $6   |
|                         | **≈ 79% Gross Margin**        |

---

## 8. Authentication & Access Control

- Supabase Auth (JWT, email/OAuth)
- Row-Level Security on all tables
- Role-based RBAC (`user`, `admin`, `viewer`)
- JWTs passed to Supabase client and database

---

## 9. Stripe Billing

| Plan    | Price  | Channels | Messages | Notes               |
| ------- | ------ | -------- | -------- | ------------------- |
| Free    | $0     | 1        | 5,000/mo | Community tier      |
| Pro     | $29/mo | 3        | 100,000  | Most users          |
| Add-on  | $5     | +1       | —        | Additional channels |
| Overage | $0.30  | —        | per 1k   | Usage beyond quota  |

---

## 10. Integration Requirements

### 10.1 Discord

- **Scopes**: `bot`, `applications.commands`
- **Permissions**: `Send`, `Manage Messages`, `Use Slash Commands`
- **Webhook Support**: via Discord Interactions
- No gateway or privileged intents required

### 10.2 Slack

- OAuth + Bot Token
- Events API: `message.channels`, `app_mention`
- Slack Web API for replies

### 10.3 Telegram

- Bot Token via BotFather
- Webhook registered per bot instance
- POST → Cloudflare Worker → response

---

## 11. CI/CD & DevOps

- **Frontend**: Vercel auto-deploy (GitHub → Vercel)
- **Workers**: Wrangler deploy (`wrangler.toml` per channel)
- **Supabase**: CLI migrations, schema versioning
- **Secrets**: Managed via Wrangler and Vercel dashboard
- **Local Dev**: Supabase CLI + Wrangler + ngrok for webhook testing

---

## 12. Monitoring & Error Handling

- **Sentry**: Error tracking (frontend + Workers)
- **Cloudflare Logs**: Traffic, latency, errors
- **Supabase Logs**: SQL errors, auth events
- **Slack Alerts**: For webhook failures, moderation thresholds

---

## 13. Milestones

| Date | Milestone                        |
| ---- | -------------------------------- |
| W0   | Discord + Telegram worker live   |
| W+2  | Slack integration completed      |
| W+3  | Billing + plan enforcement       |
| W+4  | Public beta with Stripe checkout |
| W+6  | Dashboard polish & prompt editor |
| W+8  | GA launch                        |

---

## 14. Risks & Mitigations

| Risk                          | Mitigation                             |
| ----------------------------- | -------------------------------------- |
| High LLM latency              | Cache completions where possible       |
| Discord webhook change        | Periodic verification + reconnect flow |
| Supabase RLS misconfiguration | CI tests on policy coverage            |
| Overload on Free tier         | Soft limits + upgrade banner           |

---

## 15. Success Criteria

- 1,000+ agents created in 90 days
- 80% of active users deploy to ≥2 channels
- Infra margin > 75% across paying users
- < 3 min average response time per AI reply
- < 1% message moderation false positive rate
