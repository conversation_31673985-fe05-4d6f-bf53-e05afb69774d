"use server";

import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";

const ApiKeySchema = z.object({
  name: z.string().min(1, "API key name is required"),
  platform: z.enum(["discord", "telegram", "slack"]),
  expires_at: z.string().nullable(),
  organization_id: z.string().uuid("Invalid organization ID"),
});

export interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;
  platform: "discord" | "telegram" | "slack";
  created_at: string;
  last_used_at: string | null;
  expires_at: string | null;
  // Note: The full key is only shown once upon creation and is not stored directly.
  // The 'key' field here is for client-side display purposes if needed after creation.
  key?: string;
}

// Helper function to generate cryptographically secure random string
const generateSecureRandomString = (length: number): string => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array)
    .map((byte) => byte.toString(16).padStart(2, "0"))
    .join("")
    .substring(0, length);
};

export async function createApiKey(formData: {
  name: string;
  platform: "discord" | "telegram" | "slack";
  expirationPeriod: string;
  organization_id: string;
}) {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) { 
    return { error: "User not authenticated" };
  }

  let expires_at: string | null = null;
  if (formData.expirationPeriod === "30days") {
    const date = new Date();
    date.setDate(date.getDate() + 30);
    expires_at = date.toISOString();
  } else if (formData.expirationPeriod === "90days") {
    const date = new Date();
    date.setDate(date.getDate() + 90);
    expires_at = date.toISOString();
  } else if (formData.expirationPeriod === "1year") {
    const date = new Date();
    date.setFullYear(date.getFullYear() + 1);
    expires_at = date.toISOString();
  }

  const validatedFields = ApiKeySchema.safeParse({
    name: formData.name,
    platform: formData.platform,
    expires_at,
    organization_id: formData.organization_id,
  });

  if (!validatedFields.success) {
    return {
      error: "Invalid input.",
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { name, platform, organization_id } = validatedFields.data;

  const keyPrefix = `cmz_${platform.substring(0, 2)}_${generateSecureRandomString(4)}`;
  const secret = generateSecureRandomString(32);
  const fullKey = `${keyPrefix}_${secret}`;

  // In a real app, you'd hash the 'secret' part of the key before storing.
  // For simplicity here, we'll store a hash of the full key, but ideally, only the secret part is hashed.
  // The crypto.subtle API is available in Edge functions and modern Node.js.
  const keyHashBuffer = await crypto.subtle.digest(
    "SHA-256",
    new TextEncoder().encode(fullKey) // Ideally, hash only the 'secret' part
  );
  const keyHash = Buffer.from(keyHashBuffer).toString("hex");

  const { data, error } = await supabase
    .from("api_keys")
    .insert({
      name,
      organization_id,
      key_prefix: keyPrefix,
      key_hash: keyHash, // Store the hash
      // Assuming 'platform' column exists or will be added to api_keys table
      platform: platform, 
      created_by: user.id,
      expires_at: validatedFields.data.expires_at,
      scopes: ["read", "write"], // Default scopes
    })
    .select(`
      id,
      name,
      key_prefix,
      platform,
      created_at,
      last_used_at,
      expires_at
    `)
    .single();

  if (error) {
    console.error("Error creating API key:", error);
    return { error: "Could not create API key. " + error.message };
  }

  revalidatePath("/settings/api-keys"); // Or the relevant path
  return { data: { ...data, key: fullKey } }; // Return the full key ONCE for the user to copy
}

export async function listApiKeys(organizationId: string): Promise<{ data?: ApiKey[]; error?: string }> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("api_keys")
    .select(`
      id,
      name,
      key_prefix,
      platform,
      created_at,
      last_used_at,
      expires_at
    `)
    .eq("organization_id", organizationId)

  if (error) {
    console.error("Error listing API keys:", error);
    return { error: "Could not retrieve API keys." };
  }
  // TODO(you): The 'revoked' field is not in the db schema, using revoked_at IS NULL instead.
  // The client-side ApiKey type has 'revoked: boolean'. This needs to be reconciled.
  // For now, we'll treat keys not having a 'revoked_at' timestamp as not revoked.
  return { data: data as ApiKey[] };
}

export async function revokeApiKey(apiKeyId: string, organizationId: string) {
  const supabase = await createClient();
  
  // Instead of deleting, we mark as revoked by setting 'revoked_at'
  // This assumes you add a 'revoked_at' (TIMESTAMPTZ) column to your 'api_keys' table.
  // If you prefer hard deletes, ensure RLS allows it and use .delete().eq('id', apiKeyId)
  const { error } = await supabase
    .from("api_keys")
    .update({ revoked_at: new Date().toISOString() })
    .eq("id", apiKeyId)
    .eq("organization_id", organizationId); // Ensure user can only revoke keys for their org

  if (error) {
    console.error("Error revoking API key:", error);
    return { error: "Could not revoke API key. " + error.message };
  }

  revalidatePath("/settings/api-keys");
  return { success: true };
}

export async function renameApiKey(apiKeyId: string, newName: string, organizationId: string) {
  const supabase = await createClient();

  if (!newName.trim()) {
    return { error: "New name cannot be empty." };
  }

  const { error } = await supabase
    .from("api_keys")
    .update({ name: newName, updated_at: new Date().toISOString() })
    .eq("id", apiKeyId)
    .eq("organization_id", organizationId);

  if (error) {
    console.error("Error renaming API key:", error);
    return { error: "Could not rename API key. " + error.message };
  }

  revalidatePath("/settings/api-keys");
  return { success: true };
}
