"use client"

import { useState } from "react"
import { motion, useReducedMotion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  CreditCard, 
  FileText, 
  ChevronRight, 
  AlertCircle,
  BadgeCheck,
  Zap,
  Terminal,
  Calendar
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BillingSubscription } from "@/components/billing/subscription"
import { BillingPaymentMethods } from "@/components/billing/payment-methods"
import { BillingUsage } from "@/components/billing/usage"
import { BillingInvoices } from "@/components/billing/invoices"

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const prefersReducedMotion = useReducedMotion()
  
  // Mock data - in a real app this would come from the billing API
  const subscription = {
    plan: "Pro",
    status: "active",
    interval: "monthly",
    amount: 49,
    currency: "USD",
    currentPeriodEnd: new Date("2025-05-28"),
    trialEnd: null,
    cancelAtPeriodEnd: false,
  }
  
  const usageStats = {
    aiInteractions: {
      used: 384,
      total: 1000,
      percentUsed: 38.4,
    },
    agents: {
      used: 2,
      total: 3,
    },
    integrations: {
      used: 2,
      total: 3,
    },
    customTraining: {
      available: true,
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Billing</h1>
        <p className="text-muted-foreground">Manage your subscription, payment methods, and billing information</p>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <motion.div 
            className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
            initial={prefersReducedMotion ? { opacity: 1 } : "hidden"}
            animate={prefersReducedMotion ? { opacity: 1 } : "visible"}
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1
                }
              }
            }}
          >
            {/* Current Plan */}
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-lg">
                    Current Plan
                    {subscription.status === "active" && (
                      <Badge variant="outline" className="ml-2 bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-400">
                        <BadgeCheck className="mr-1 h-3 w-3" />
                        Active
                      </Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex items-baseline space-x-2">
                    <div className="text-3xl font-bold">{subscription.plan}</div>
                    <div className="text-muted-foreground">
                      ${subscription.amount}/{subscription.interval === "monthly" ? "mo" : "yr"}
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center text-sm text-muted-foreground">
                    <Calendar className="mr-2 h-4 w-4" />
                    Renews on {subscription.currentPeriodEnd.toLocaleDateString()}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full" onClick={() => setActiveTab("subscription")}>
                    Manage Plan
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
            
            {/* AI Interactions */}
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">AI Interactions</CardTitle>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2">
                    <div className="flex items-baseline justify-between">
                      <div className="text-3xl font-bold">{usageStats.aiInteractions.used.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">of {usageStats.aiInteractions.total.toLocaleString()}</div>
                    </div>
                    <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
                      <div 
                        className="h-full bg-primary" 
                        style={{ width: `${usageStats.aiInteractions.percentUsed}%` }} 
                      />
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {usageStats.aiInteractions.percentUsed}% used this month
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full" onClick={() => setActiveTab("usage")}>
                    View Usage
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
            
            {/* Active Agents */}
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Active Agents</CardTitle>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2">
                    <div className="flex items-baseline justify-between">
                      <div className="text-3xl font-bold">{usageStats.agents.used}</div>
                      <div className="text-sm text-muted-foreground">of {usageStats.agents.total}</div>
                    </div>
                    <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
                      <div 
                        className="h-full bg-primary" 
                        style={{ width: `${(usageStats.agents.used / usageStats.agents.total) * 100}%` }} 
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full" onClick={() => setActiveTab("usage")}>
                    View Usage
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          </motion.div>
          
          {/* Payment Method / Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Payment Method</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="flex items-center space-x-4">
                  <div className="h-14 w-14 flex items-center justify-center rounded-lg bg-primary/10">
                    <CreditCard className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <div className="font-semibold">•••• •••• •••• 4242</div>
                    <div className="text-sm text-muted-foreground">Expires 04/2026</div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" onClick={() => setActiveTab("payment-methods")}>
                  Manage Payment Methods
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" className="h-20 flex flex-col gap-1">
                    <FileText className="h-5 w-5" />
                    <span>Download Invoice</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col gap-1">
                    <Zap className="h-5 w-5" />
                    <span>Upgrade Plan</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Usage Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Usage Summary</CardTitle>
              <CardDescription>Your current resource usage and limits</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium text-muted-foreground mb-1">AI Interactions</div>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{usageStats.aiInteractions.used}</div>
                      <div className="text-sm text-muted-foreground">/ {usageStats.aiInteractions.total}</div>
                    </div>
                  </div>
                  
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium text-muted-foreground mb-1">Agents</div>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{usageStats.agents.used}</div>
                      <div className="text-sm text-muted-foreground">/ {usageStats.agents.total}</div>
                    </div>
                  </div>
                  
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium text-muted-foreground mb-1">Integrations</div>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{usageStats.integrations.used}</div>
                      <div className="text-sm text-muted-foreground">/ {usageStats.integrations.total}</div>
                    </div>
                  </div>
                  
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium text-muted-foreground mb-1">Custom Training</div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-400">
                        Available
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Need Help Section */}
          <Alert variant="default" className="bg-muted/50">
            <Terminal className="h-4 w-4" />
            <AlertTitle>Need help with billing?</AlertTitle>
            <AlertDescription>
              Contact our support team at <a href="mailto:<EMAIL>" className="font-medium underline underline-offset-4"><EMAIL></a> or visit our <a href="/help/billing" className="font-medium underline underline-offset-4">billing documentation</a>.
            </AlertDescription>
          </Alert>
        </TabsContent>
        
        <TabsContent value="subscription">
          <BillingSubscription />
        </TabsContent>
        
        <TabsContent value="payment-methods">
          <BillingPaymentMethods />
        </TabsContent>
        
        <TabsContent value="usage">
          <BillingUsage />
        </TabsContent>
        
        <TabsContent value="invoices">
          <BillingInvoices />
        </TabsContent>
      </Tabs>
    </div>
  )
}