"use client"

import { Navbar } from "@/components/Navbar";
import { Sidebar } from "@/components/Sidebar";
import { SidebarProvider, useSidebar } from "@/hooks/use-sidebar";
import { motion } from "framer-motion";
import type React from "react";
import { Suspense } from "react";

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <DashboardLayoutContent>{children}</DashboardLayoutContent>
    </SidebarProvider>
  );
}

function DashboardLayoutContent({ children }: { children: React.ReactNode }) {
  const { collapsed } = useSidebar();
  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />
      <div className="flex flex-1">
        <Suspense>
          <Sidebar />
          <motion.main
            className="flex-1 p-6"
            initial={{ marginLeft: collapsed ? 80 : 250 }}
            animate={{ marginLeft: collapsed ? 80 : 250 }}
            transition={{ duration: 0.2, ease: [0.33, 1, 0.68, 1] }}
          >
            {children}
          </motion.main>
        </Suspense>
      </div>
    </div>
  );
}
