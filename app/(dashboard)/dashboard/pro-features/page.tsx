"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RoleGate } from "@/components/auth/role-gate"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export default function ProFeaturesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Pro Features</h1>
        <p className="text-muted-foreground">Advanced features for Pro and Enterprise users</p>
      </div>

      <RoleGate
        allowedRoles={["pro", "enterprise"]}
        fallback={
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Restricted</AlertTitle>
            <AlertDescription>
              You need a Pro or Enterprise subscription to access these features.
              <div className="mt-4">
                <Button variant="outline">Upgrade Now</Button>
              </div>
            </AlertDescription>
          </Alert>
        }
      >
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle>Advanced Analytics</CardTitle>
                <Badge>Pro</Badge>
              </div>
              <CardDescription>Detailed insights into your community</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Access detailed analytics about your community engagement, moderation actions, and growth metrics.
              </p>
              <Button className="mt-4 w-full">View Analytics</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle>Custom AI Training</CardTitle>
                <Badge>Pro</Badge>
              </div>
              <CardDescription>Train AI on your specific content</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Upload custom data to train your AI agents on your specific community content and terminology.
              </p>
              <Button className="mt-4 w-full">Configure Training</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle>Bulk Operations</CardTitle>
                <Badge>Pro</Badge>
              </div>
              <CardDescription>Batch actions for efficiency</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Perform bulk operations on conversations, moderation actions, and community management tasks.
              </p>
              <Button className="mt-4 w-full">Open Bulk Tools</Button>
            </CardContent>
          </Card>
        </div>
      </RoleGate>
    </div>
  )
}
