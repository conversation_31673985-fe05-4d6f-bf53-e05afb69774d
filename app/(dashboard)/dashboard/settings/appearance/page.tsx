"use client"

import { useState } from "react"
import { useTheme } from "next-themes"
import { motion } from "framer-motion"
import { <PERSON>, Sun, Monitor, Check } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AppearanceSettingsPage() {
  const { theme, setTheme } = useTheme()
  const [activeTab, setActiveTab] = useState("theme")

  // Define theme options
  const themeOptions = [
    {
      value: "light",
      label: "Light",
      icon: Sun,
      description: "Warm light mode with beige background (#FBF6F2) and dark text",
    },
    {
      value: "dark",
      label: "Dark",
      icon: Moon,
      description: "Dark mode with dark background and light text",
    },
    {
      value: "system",
      label: "System",
      icon: Monitor,
      description: "Follows your system's theme preference",
    },
  ]

  // Define accent color options
  const accentColors = [
    { value: "violet", color: "#7C3AED", label: "Violet (Default)" },
    { value: "blue", color: "#3B82F6", label: "Blue" },
    { value: "green", color: "#10B981", label: "Green" },
    { value: "red", color: "#EF4444", label: "Red" },
    { value: "orange", color: "#F97316", label: "Orange" },
    { value: "pink", color: "#EC4899", label: "Pink" },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Appearance</h1>
        <p className="text-muted-foreground">Customize the appearance of the application</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="theme">Theme</TabsTrigger>
          <TabsTrigger value="accent">Accent Color</TabsTrigger>
        </TabsList>
        <TabsContent value="theme" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Theme</CardTitle>
              <CardDescription>Select the theme for the application</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <RadioGroup
                value={theme}
                onValueChange={(value) => setTheme(value)}
                className="grid grid-cols-1 gap-4 md:grid-cols-3"
              >
                {themeOptions.map((option) => (
                  <div key={option.value} className="relative">
                    <RadioGroupItem
                      value={option.value}
                      id={`theme-${option.value}`}
                      className="peer sr-only"
                      aria-label={option.label}
                    />
                    <Label
                      htmlFor={`theme-${option.value}`}
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <div className="flex flex-col items-center gap-2">
                        <option.icon className="h-6 w-6" />
                        <div className="font-semibold">{option.label}</div>
                      </div>
                      <p className="text-center text-sm text-muted-foreground">{option.description}</p>
                      {theme === option.value && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute right-2 top-2 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-primary-foreground"
                        >
                          <Check className="h-3 w-3" />
                        </motion.div>
                      )}
                    </Label>
                  </div>
                ))}
              </RadioGroup>

              {/* Theme preview with updated light mode background (#FBF6F2) */}
              <div className="mt-3 flex items-center gap-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-background shadow">
                  <div className="h-5 w-5 rounded-md bg-foreground" />
                </div>
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-primary shadow">
                  <div className="h-5 w-5 rounded-md bg-primary-foreground" />
                </div>
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-secondary shadow">
                  <div className="h-5 w-5 rounded-md bg-secondary-foreground" />
                </div>
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-accent shadow">
                  <div className="h-5 w-5 rounded-md bg-accent-foreground" />
                </div>
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-destructive shadow">
                  <div className="h-5 w-5 rounded-md bg-destructive-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="accent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accent Color</CardTitle>
              <CardDescription>Choose the accent color for the application</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 md:grid-cols-6">
                {accentColors.map((color) => (
                  <div key={color.value} className="text-center">
                    <button
                      className="group relative mx-auto mb-2 h-12 w-12 overflow-hidden rounded-full focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      style={{ backgroundColor: color.color }}
                      aria-label={`Set accent color to ${color.label}`}
                      disabled={color.value === "violet"} // Default color is disabled
                    >
                      {color.value === "violet" && (
                        <div className="absolute inset-0 flex items-center justify-center text-white">
                          <Check className="h-6 w-6" />
                        </div>
                      )}
                      <span className="absolute inset-0 flex items-center justify-center text-white opacity-0 transition-opacity group-hover:opacity-100">
                        <Check className="h-6 w-6" />
                      </span>
                    </button>
                    <div className="text-xs">{color.label}</div>
                  </div>
                ))}
              </div>
              <div className="mt-6 rounded-md bg-muted p-4 text-sm">
                <p>
                  <strong>Note:</strong> Custom accent colors are coming soon. Currently, the default violet accent
                  color is used throughout the application.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
