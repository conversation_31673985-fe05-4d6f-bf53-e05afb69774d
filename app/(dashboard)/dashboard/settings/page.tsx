"use client"

import Link from "next/link"
import { User, <PERSON><PERSON>, <PERSON>, Shield, Key, CreditCard, Users, ChevronRight } from "lucide-react"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function SettingsPage() {
  const settingsCategories = [
    {
      title: "Profile",
      description: "Manage your personal information and account settings",
      icon: User,
      href: "/dashboard/settings/profile",
    },
    {
      title: "Appearance",
      description: "Customize the look and feel of the application",
      icon: Palette,
      href: "/dashboard/settings/appearance",
    },
    {
      title: "Notifications",
      description: "Configure how and when you receive notifications",
      icon: Bell,
      href: "/dashboard/settings/notifications",
    },
    {
      title: "Security",
      description: "Manage your password, 2FA, and security settings",
      icon: Shield,
      href: "/dashboard/settings/security",
    },
    {
      title: "API Keys",
      description: "Create and manage API keys for programmatic access",
      icon: Key,
      href: "/dashboard/settings/api-keys",
    },
    {
      title: "Billing",
      description: "Manage your subscription, payment methods, and invoices",
      icon: CreditCard,
      href: "/dashboard/billing",
    },
    {
      title: "Team",
      description: "Invite team members and manage permissions",
      icon: Users,
      href: "/dashboard/settings/team",
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">Manage your account settings and preferences</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {settingsCategories.map((category) => (
          <Link key={category.title} href={category.href} className="block">
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-4">
                  <div className="rounded-md bg-primary/10 p-2">
                    <category.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{category.title}</CardTitle>
                    <CardDescription className="mt-1">{category.description}</CardDescription>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}
