"use server"

import { createClient } from "@/lib/supabase/server";
import console from "console";
import { createHash, randomBytes } from "crypto";
import { revalidatePath } from "next/cache";
import { z } from "zod";

// Types
export type Platform = "discord" | "telegram" | "slack";
export type ExpirationPeriod = "never" | "30days" | "90days" | "1year";

// Client-side API Key interface with platform information
export interface ApiKey {
  id: string;
  name: string;
  key?: string; // Only present when creating a new key
  key_prefix?: string;
  platform: Platform;
  createdAt: string;
  lastUsed: string | null;
  expiresAt: string | null;
  revoked: boolean;
}

// Validation schema
const ApiKeySchema = z.object({
  name: z.string().min(1, "API key name is required"),
  platform: z.enum(["discord", "telegram", "slack"]),
  expirationPeriod: z
    .enum(["never", "30days", "90days", "1year"])
    .default("never"),
});

// Helper function to generate secure random string
function generateSecureRandomString(length: number): string {
  return randomBytes(Math.ceil(length / 2))
    .toString("hex")
    .substring(0, length);
}

// Create a new API key
export async function createApiKey(data: {
  name: string;
  platform: Platform;
  expirationPeriod: ExpirationPeriod;
}) {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return { error: { message: "Not authenticated" } };
  }

  // Validate input data
  const validation = ApiKeySchema.safeParse(data);
  if (!validation.success) {
    return {
      error: {
        message: "Invalid input data",
        details: validation.error.format(),
      },
    };
  }

  const { name, platform, expirationPeriod } = validation.data;

  // Fetch organization_id for the current user
  const { data: orgMember, error: orgError } = await supabase
    .from("organization_members")
    .select("organization_id")
    .eq("user_id", user.id)
    .in("role", ["owner", "admin"])
    .limit(1)
    .maybeSingle();

  if (orgError || !orgMember) {
    console.error(
      "Error fetching organization or no qualifying organization found for user:",
      user.id,
      orgError
    );
    return {
      error: {
        message:
          "Failed to determine organization for API key creation. Ensure you are an admin or owner of an organization.",
      },
    };
  }
  const organizationId = orgMember.organization_id;

  // Calculate expiration date based on selection
  let expires_at: string | null = null;
  if (expirationPeriod === "30days") {
    const date = new Date();
    date.setDate(date.getDate() + 30);
    expires_at = date.toISOString();
  } else if (expirationPeriod === "90days") {
    const date = new Date();
    date.setDate(date.getDate() + 90);
    expires_at = date.toISOString();
  } else if (expirationPeriod === "1year") {
    const date = new Date();
    date.setFullYear(date.getFullYear() + 1);
    expires_at = date.toISOString();
  }

  try {
    // Define scopes based on platform and default access
    const scopes = [`platform:${platform}`, "read"];

    // Generate key components
    const keyPrefix = `cmz_${platform.substring(
      0,
      2
    )}_${generateSecureRandomString(4)}`;
    const keySecret = generateSecureRandomString(24);
    const fullKey = `${keyPrefix}_${keySecret}`;

    // Hash the key for storage
    const keyHash = createHash("sha256").update(fullKey).digest("hex");

    // Insert the new API key directly into the database
    const { data: insertedKey, error: insertError } = await supabase
      .from("api_keys")
      .insert({
        organization_id: organizationId,
        name,
        key_prefix: keyPrefix,
        key_hash: keyHash,
        scopes,
        platform,
        created_by: user.id,
        expires_at,
      })
      .select(
        `
        id,
        name,
        key_prefix,
        platform,
        created_at,
        last_used_at,
        expires_at
      `
      )
      .single();

    if (insertError) {
      console.error("Error creating API key:", insertError);
      return {
        error: { message: "Failed to create API key: " + insertError.message },
      };
    }

    revalidatePath("/dashboard/settings/api-keys");

    // Return the API key data with the full key (only time it's available)
    return {
      data: {
        ...insertedKey,
        key: fullKey,
        createdAt: insertedKey.created_at,
        lastUsed: insertedKey.last_used_at,
        expiresAt: insertedKey.expires_at,
        revoked: false,
      } as ApiKey,
    };
  } catch (error: any) {
    console.error("Unexpected error creating API key:", error);
    return {
      error: {
        message: "Failed to create API key due to an unexpected error.",
      },
    };
  }
}

// Get all API keys for the current user
export async function getApiKeys() {
  const supabase = await createClient();

  // Get the session to ensure the user is authenticated
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    console.error("User not authenticated");
    return { error: { message: "Not authenticated" } };
  }

  try {
    // First, find organizations the user belongs to
    const { data: userOrgs, error: orgError } = await supabase
      .from("organization_members")
      .select("organization_id")
      .eq("user_id", user.id)
      .in("role", ["owner", "admin"]);

    if (orgError) {
      console.error("Error fetching user organizations:", orgError);
      return { error: { message: "Failed to fetch user organizations" } };
    }

    if (!userOrgs || userOrgs.length === 0) {
      return { data: [] }; // User has no organizations
    }

    const orgIds = userOrgs.map((org) => org.organization_id);

    // Get API keys for all user's organizations
    const { data: apiKeysData, error: keysError } = await supabase
      .from("api_keys")
      .select(
        `
        id,
        name,
        key_prefix,
        platform,
        created_at,
        last_used_at,
        expires_at,
        revoked_at,
        organization_id
      `
      )
      .in("organization_id", orgIds)
      .order("created_at", { ascending: false });

    if (keysError) {
      console.error("Error fetching API keys:", keysError);
      return { error: { message: "Failed to fetch API keys" } };
    }

    if (!apiKeysData || apiKeysData.length === 0) {
      return { data: [] }; // No API keys found
    }

    // Transform database result to client API key format
    const clientApiKeys: ApiKey[] = apiKeysData.map((key) => {
      // Use the platform field directly from the api_keys table
      // Default to "discord" if platform is not set
      const platform: Platform = (key.platform as Platform) || "discord";

      return {
        id: key.id,
        name: key.name,
        key_prefix: key.key_prefix,
        platform: platform,
        createdAt: key.created_at,
        lastUsed: key.last_used_at,
        expiresAt: key.expires_at,
        revoked: !!key.revoked_at, // Convert revoked_at timestamp to boolean
      };
    });

    return { data: clientApiKeys };
  } catch (error) {
    console.error("Error fetching API keys:", error);
    return { error: { message: "Failed to fetch API keys" } };
  }
}

// Revoke an API key
export async function revokeApiKey(keyId: string) {
  const supabase = await createClient();

  // Get the session to ensure the user is authenticated
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return { error: { message: "Not authenticated" } };
  }

  try {
    // First find organizations the user is admin/owner of
    const { data: userOrgs, error: orgError } = await supabase
      .from("organization_members")
      .select("organization_id")
      .eq("user_id", user.id)
      .in("role", ["owner", "admin"]);

    if (orgError) {
      return { error: { message: "Failed to verify permissions" } };
    }

    if (!userOrgs || userOrgs.length === 0) {
      return {
        error: { message: "You don't have permission to revoke this API key" },
      };
    }

    const orgIds = userOrgs.map((org) => org.organization_id);

    // Check if key belongs to user's organization
    const { data: keyData, error: keyError } = await supabase
      .from("api_keys")
      .select("id, organization_id")
      .eq("id", keyId)
      .in("organization_id", orgIds)
      .single();

    if (keyError || !keyData) {
      return {
        error: {
          message:
            "API key not found or you don't have permission to revoke it",
        },
      };
    }

    // Set revoked_at timestamp instead of a boolean flag
    const { error: updateError } = await supabase
      .from("api_keys")
      .update({ revoked_at: new Date().toISOString() })
      .eq("id", keyId);

    if (updateError) {
      return {
        error: { message: "Failed to revoke API key: " + updateError.message },
      };
    }

    // Revalidate the API keys page
    revalidatePath("/dashboard/settings/api-keys");

    return { success: true };
  } catch (error: any) {
    console.error("Error revoking API key:", error);
    return { error: { message: "Failed to revoke API key" } };
  }
}

// Update an API key's name
export async function updateApiKeyName(keyId: string, name: string) {
  const supabase = await createClient();

  // Get the session to ensure the user is authenticated
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return { error: { message: "Not authenticated" } };
  }

  if (!name.trim()) {
    return { error: { message: "Name is required" } };
  }

  try {
    // First find organizations the user is admin/owner of
    const { data: userOrgs, error: orgError } = await supabase
      .from("organization_members")
      .select("organization_id")
      .eq("user_id", user.id)
      .in("role", ["owner", "admin"]);

    if (orgError) {
      return { error: { message: "Failed to verify permissions" } };
    }

    if (!userOrgs || userOrgs.length === 0) {
      return {
        error: { message: "You don't have permission to update this API key" },
      };
    }

    const orgIds = userOrgs.map((org) => org.organization_id);

    // Check if key belongs to user's organization
    const { data: keyData, error: keyError } = await supabase
      .from("api_keys")
      .select("id, organization_id")
      .eq("id", keyId)
      .in("organization_id", orgIds)
      .single();

    if (keyError || !keyData) {
      return {
        error: {
          message:
            "API key not found or you don't have permission to update it",
        },
      };
    }

    // Update the API key name
    const { error: updateError } = await supabase
      .from("api_keys")
      .update({ name: name.trim() })
      .eq("id", keyId);

    if (updateError) {
      return {
        error: { message: "Failed to update API key: " + updateError.message },
      };
    }

    // Revalidate the API keys page
    revalidatePath("/dashboard/settings/api-keys");

    return { success: true };
  } catch (error: any) {
    console.error("Error updating API key:", error);
    return { error: { message: "Failed to update API key" } };
  }
}
