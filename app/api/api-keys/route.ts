// app/api/keys/route.ts
import { createClient } from "@/lib/supabase/server";
import { createHash, randomBytes } from "crypto";
import { NextRequest, NextResponse } from "next/server";


export async function POST(req: NextRequest) {
  const { name, platform = "discord" } = await req.json();
  if (!name)
    return NextResponse.json(
      { error: "Key name is required" },
      { status: 400 }
    );

  const supabase = await createClient();

  // ALWAYS re-validate
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

  const userId = user.id;

  // Generate key components with platform-specific prefix
  const keyPrefix = `cmz_${platform.substring(0, 2)}_${randomBytes(4).toString(
    "hex"
  )}`;
  const keySecret = randomBytes(24).toString('hex');
  const fullApiKey = `${keyPrefix}_${keySecret}`;

  // Hash the full key for storage
  const keyHash = createHash("sha256").update(fullApiKey).digest("hex");

  // Define default scopes
  const scopes = [`platform:${platform}`, "read"];

  const { data, error } = await supabase
    .from("api_keys")
    .insert({
      organization_id: userId, // adjust if you need org lookup first
      name,
      key_prefix: keyPrefix,
      key_hash: keyHash,
      platform,
      scopes,
      created_by: userId,
    })
    .select()
    .single();

  if (error) return NextResponse.json({ error: error.message }, { status: 500 });

  return NextResponse.json({
    id: data.id,
    name: data.name,
    key: fullApiKey, // Include the complete API key
    key_prefix: data.key_prefix,
    platform: data.platform,
    created_at: data.created_at,
    expires_at: data.expires_at,
  });
}
