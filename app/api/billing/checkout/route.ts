import { NextResponse } from 'next/server';
import { createCheckoutSession, createOrRetrieveCustomer } from '@/lib/stripe';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  try {
    // Get the price ID and return URL from the request
    const { priceId, returnUrl } = await request.json();
    
    if (!priceId) {
      return NextResponse.json({ error: 'Price ID is required' }, { status: 400 });
    }


    const supabase = await createClient();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's profile information for customer creation
    const { data: profile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('id', user.id)
      .single();

    // Create or retrieve a customer
    const customerId = await createOrRetrieveCustomer(
      user.id,
      user.email || '',
      profile?.full_name
    );

    // Create a checkout session
    const session = await createCheckoutSession({
      customerId,
      priceId,
      returnUrl: returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
    });

    if (!session.url) {
      return NextResponse.json({ error: 'Failed to create checkout session' }, { status: 500 });
    }

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json({ error: 'An error occurred creating checkout session' }, { status: 500 });
  }
}