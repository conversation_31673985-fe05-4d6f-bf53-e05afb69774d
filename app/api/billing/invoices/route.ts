import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16', // Use the latest API version
});

export async function GET() {
  try {
    // Initialize Supabase client
    const supabase = await createClient();
    
    // Get the user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get user profile including the Stripe customer ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();
    
    if (profileError || !profile) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch user profile' }, 
        { status: 500 }
      );
    }
    
    // If user doesn't have a Stripe customer ID, return empty array
    if (!profile.stripe_customer_id) {
      return NextResponse.json({ invoices: [] });
    }
    
    // Fetch invoices from Stripe
    const invoices = await stripe.invoices.list({
      customer: profile.stripe_customer_id,
      limit: 24, // Last 24 invoices should be enough
      expand: ['data.payment_intent'],
    });
    
    // Format invoices
    const formattedInvoices = invoices.data.map(invoice => {
      let paymentMethodDetails = null;
      
      // Try to extract payment method details
      if (invoice.payment_intent && typeof invoice.payment_intent !== 'string') {
        const paymentIntent = invoice.payment_intent;
        if (paymentIntent.payment_method && typeof paymentIntent.payment_method !== 'string') {
          const paymentMethod = paymentIntent.payment_method;
          if (paymentMethod.type === 'card' && paymentMethod.card) {
            paymentMethodDetails = {
              type: 'card',
              card: {
                brand: paymentMethod.card.brand,
                last4: paymentMethod.card.last4,
              },
            };
          }
        }
      }
      
      return {
        id: invoice.id,
        number: invoice.number,
        status: invoice.status,
        amount_due: invoice.amount_due / 100, // Convert from cents
        amount_paid: invoice.amount_paid / 100, // Convert from cents
        created: invoice.created,
        invoice_pdf: invoice.invoice_pdf,
        payment_method_details: paymentMethodDetails,
      };
    });
    
    return NextResponse.json({ invoices: formattedInvoices });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invoices' }, 
      { status: 500 }
    );
  }
}