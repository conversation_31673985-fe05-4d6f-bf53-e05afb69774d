import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16', // Use the latest API version
});

export async function GET() {
  try {
    // Initialize Supabase client
    const supabase = await createClient();
    // Get the user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get user profile including the Stripe customer ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();
    
    if (profileError || !profile) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch user profile' }, 
        { status: 500 }
      );
    }
    
    // If user doesn't have a Stripe customer ID, return empty array
    if (!profile.stripe_customer_id) {
      return NextResponse.json({ paymentMethods: [] });
    }
    
    // Get customer to find default payment method
    const customer = await stripe.customers.retrieve(profile.stripe_customer_id, {
      expand: ['invoice_settings.default_payment_method']
    });
    
    // Fetch payment methods from Stripe
    const paymentMethods = await stripe.paymentMethods.list({
      customer: profile.stripe_customer_id,
      type: 'card',
    });
    
    // Format and mark default payment method
    const defaultPaymentMethodId = customer.invoice_settings?.default_payment_method?.id;
    
    const formattedPaymentMethods = paymentMethods.data.map(method => ({
      id: method.id,
      type: method.type,
      card: {
        brand: method.card?.brand,
        last4: method.card?.last4,
        exp_month: method.card?.exp_month,
        exp_year: method.card?.exp_year,
      },
      billing_details: {
        address: {
          line1: method.billing_details.address?.line1 || '',
          city: method.billing_details.address?.city || '',
          state: method.billing_details.address?.state || '',
          postal_code: method.billing_details.address?.postal_code || '',
          country: method.billing_details.address?.country || '',
        }
      },
      is_default: method.id === defaultPaymentMethodId
    }));
    
    return NextResponse.json({ paymentMethods: formattedPaymentMethods });
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment methods' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    // Get the payment method ID from the request body
    const { paymentMethodId } = await request.json();
    
    if (!paymentMethodId) {
      return NextResponse.json(
        { error: 'Payment method ID is required' }, 
        { status: 400 }
      );
    }
    
    // Detach the payment method
    await stripe.paymentMethods.detach(paymentMethodId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing payment method:', error);
    return NextResponse.json(
      { error: 'Failed to remove payment method' }, 
      { status: 500 }
    );
  }
}