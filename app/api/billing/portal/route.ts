import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createPortalSession, createOrRetrieveCustomer } from '@/lib/stripe';

export async function POST(request: Request) {
  try {
    const { returnUrl } = await request.json();
    
    // Initialize Supabase client
    const supabase = await createClient();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's profile information
    const { data: profile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('id', user.id)
      .single();

    // Create or retrieve a customer
    const customerId = await createOrRetrieveCustomer(
      user.id,
      user.email || '',
      profile?.full_name
    );

    // Create a customer portal session
    const session = await createPortalSession({
      customerId,
      returnUrl: returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
    });

    if (!session.url) {
      return NextResponse.json({ error: 'Failed to create portal session' }, { status: 500 });
    }

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating portal session:', error);
    return NextResponse.json({ error: 'An error occurred creating portal session' }, { status: 500 });
  }
}