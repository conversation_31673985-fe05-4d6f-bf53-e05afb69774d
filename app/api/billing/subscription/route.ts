import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16', // Use the latest API version
});

export async function GET() {
  try {
    // Initialize Supabase client
    const supabase = await createClient();
    
    // Get the user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get user profile including the Stripe customer ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id, role')
      .eq('id', userId)
      .single();
    
    if (profileError || !profile) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch user profile' }, 
        { status: 500 }
      );
    }
    
    // If user doesn't have a Stripe customer ID, they're on the free plan
    if (!profile.stripe_customer_id) {
      return NextResponse.json({
        subscription: {
          id: null,
          status: 'active',
          plan: 'free',
          current_period_start: null,
          current_period_end: null,
          cancel_at_period_end: false,
          price_id: null,
          amount: 0,
          interval: 'month'
        }
      });
    }
    
    // Fetch subscriptions from Stripe
    const subscriptions = await stripe.subscriptions.list({
      customer: profile.stripe_customer_id,
      status: 'all',
      expand: ['data.default_payment_method'],
      limit: 1,
    });
    
    // Get the most recent subscription
    const subscription = subscriptions.data[0];
    
    if (!subscription) {
      // User has a Stripe customer ID but no subscription - they're on the free plan
      return NextResponse.json({
        subscription: {
          id: null,
          status: 'active',
          plan: 'free',
          current_period_start: null,
          current_period_end: null,
          cancel_at_period_end: false,
          price_id: null,
          amount: 0,
          interval: 'month'
        }
      });
    }
    
    // Map Stripe subscription data to our format
    const price = subscription.items.data[0].price;
    let plan: 'free' | 'pro' | 'enterprise' = 'free';
    
    // Determine the plan based on the price ID or metadata
    if (price.metadata?.plan) {
      plan = price.metadata.plan as 'free' | 'pro' | 'enterprise';
    } else if (price.id.includes('pro')) {
      plan = 'pro';
    } else if (price.id.includes('enterprise')) {
      plan = 'enterprise';
    }
    
    // Format the subscription response
    const formattedSubscription = {
      id: subscription.id,
      status: subscription.status,
      plan,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
      price_id: price.id,
      amount: price.unit_amount ? price.unit_amount / 100 : 0,
      interval: price.recurring?.interval || 'month'
    };
    
    return NextResponse.json({ subscription: formattedSubscription });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription data' }, 
      { status: 500 }
    );
  }
}