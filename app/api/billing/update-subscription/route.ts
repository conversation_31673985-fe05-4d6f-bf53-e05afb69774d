import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16', // Use the latest API version
});

export async function PATCH(request: Request) {
  try {
    // Get data from request
    const { subscriptionId, cancel_at_period_end } = await request.json();
    
    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' }, 
        { status: 400 }
      );
    }
    
    // Initialize Supabase client
    const supabase = await createClient();
    
    // Get the user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get user profile including the Stripe customer ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();
    
    if (profileError || !profile) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch user profile' }, 
        { status: 500 }
      );
    }
    
    // Verify this subscription belongs to this customer
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    if (subscription.customer !== profile.stripe_customer_id) {
      return NextResponse.json(
        { error: 'Unauthorized to update this subscription' }, 
        { status: 403 }
      );
    }
    
    // Update the subscription with the new cancel_at_period_end value
    const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: cancel_at_period_end,
    });
    
    return NextResponse.json({ 
      success: true,
      subscription: {
        id: updatedSubscription.id,
        cancel_at_period_end: updatedSubscription.cancel_at_period_end,
      } 
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription' }, 
      { status: 500 }
    );
  }
}