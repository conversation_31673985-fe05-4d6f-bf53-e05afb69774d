import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { subDays, startOfMonth, endOfMonth, format } from 'date-fns';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '7d';
    
    // Initialize Supabase client
    const supabase = await createClient();
    
    // Get the user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get user profile including their role/plan
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();
    
    if (profileError || !profile) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch user profile' }, 
        { status: 500 }
      );
    }
    
    const userPlan = profile.role as 'free' | 'pro' | 'enterprise';
    // 'limits' property does not exist on PricingPlan. Adjust this logic as needed.
const planLimits = undefined;
    
    // Calculate date ranges based on timeframe
    let daysBack = 7;
    switch(timeframe) {
      case '30d': daysBack = 30; break;
      case '90d': daysBack = 90; break;
      default: daysBack = 7;
    }
    
    const startDate = subDays(new Date(), daysBack);
    const startDateStr = format(startDate, 'yyyy-MM-dd');
    
    // Fetch AI interactions from Supabase
    const { data: interactions, error: interactionsError } = await supabase
      .from('ai_interactions')
      .select('created_at')
      .eq('user_id', userId)
      .gte('created_at', startDateStr);
    
    if (interactionsError) {
      console.error('Error fetching AI interactions:', interactionsError);
      return NextResponse.json(
        { error: 'Failed to fetch usage data' }, 
        { status: 500 }
      );
    }
    
    // Fetch agent count from Supabase
    const { data: agents, error: agentsError } = await supabase
      .from('agents')
      .select('id')
      .eq('user_id', userId);
    
    if (agentsError) {
      console.error('Error fetching agents:', agentsError);
      return NextResponse.json(
        { error: 'Failed to fetch agents data' }, 
        { status: 500 }
      );
    }
    
    // Fetch integration count from Supabase
    const { data: integrations, error: integrationsError } = await supabase
      .from('integrations')
      .select('id')
      .eq('user_id', userId);
    
    if (integrationsError) {
      console.error('Error fetching integrations:', integrationsError);
      return NextResponse.json(
        { error: 'Failed to fetch integrations data' }, 
        { status: 500 }
      );
    }
    
    // Process interactions to create daily data
    const dailyData = processInteractionsByDay(interactions || [], startDate, daysBack);
    
    // Process interactions to create hourly data (last 24 hours)
    const hourlyData = processInteractionsByHour(interactions || []);
    
    // Process interactions to create monthly data (last 6 months)
    const monthlyData = processInteractionsByMonth(interactions || []);
    
    // Calculate total interactions in the timeframe
    const totalInteractions = interactions?.length || 0;
    
    // Build response object
    const usageData = {
      ai_interactions: {
        total: totalInteractions,
        limit: planLimits.ai_interactions_daily * daysBack,
        daily: dailyData,
        hourly: hourlyData,
        monthly: monthlyData,
      },
      agents: {
        used: agents?.length || 0,
        limit: planLimits.agents,
      },
      integrations: {
        used: integrations?.length || 0,
        limit: planLimits.integrations,
      },
      data_retention_days: planLimits.data_retention_days,
    };
    
    return NextResponse.json({ usage: usageData });
  } catch (error) {
    console.error('Error fetching usage data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch usage data' }, 
      { status: 500 }
    );
  }
}

// Helper function to process interactions by day
function processInteractionsByDay(interactions: any[], startDate: Date, daysBack: number) {
  const dailyData: { date: string; count: number }[] = [];
  const dailyCounts: { [key: string]: number } = {};
  
  // Initialize all days in the range with 0 counts
  for (let i = 0; i < daysBack; i++) {
    const date = subDays(new Date(), daysBack - i - 1);
    const dateStr = format(date, 'MMM dd');
    dailyCounts[dateStr] = 0;
  }
  
  // Count interactions by day
  interactions.forEach(interaction => {
    const date = new Date(interaction.created_at);
    const dateStr = format(date, 'MMM dd');
    if (dailyCounts[dateStr] !== undefined) {
      dailyCounts[dateStr]++;
    }
  });
  
  // Convert to array format
  Object.entries(dailyCounts).forEach(([date, count]) => {
    dailyData.push({ date, count });
  });
  
  return dailyData;
}

// Helper function to process interactions by hour (last 24 hours)
function processInteractionsByHour(interactions: any[]) {
  const hourlyData: { hour: string; count: number }[] = [];
  const hourlyCounts: { [key: string]: number } = {};
  
  // Initialize all hours with 0 counts
  for (let i = 0; i < 24; i++) {
    const hour = `${i}:00`;
    hourlyCounts[hour] = 0;
  }
  
  // Get interactions from the last 24 hours
  const last24Hours = new Date();
  last24Hours.setHours(last24Hours.getHours() - 24);
  
  // Count interactions by hour
  interactions.filter(interaction => new Date(interaction.created_at) >= last24Hours)
    .forEach(interaction => {
      const date = new Date(interaction.created_at);
      const hour = `${date.getHours()}:00`;
      hourlyCounts[hour]++;
    });
  
  // Convert to array format
  Object.entries(hourlyCounts).forEach(([hour, count]) => {
    hourlyData.push({ hour, count });
  });
  
  return hourlyData;
}

// Helper function to process interactions by month (last 6 months)
function processInteractionsByMonth(interactions: any[]) {
  const monthlyData: { month: string; count: number; limit: number }[] = [];
  const monthlyCounts: { [key: string]: number } = {};
  
  // Initialize last 6 months with 0 counts
  for (let i = 0; i < 6; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthStr = format(date, 'MMM');
    monthlyCounts[monthStr] = 0;
  }
  
  // Get interactions from the last 6 months
  const last6Months = new Date();
  last6Months.setMonth(last6Months.getMonth() - 6);
  
  // Count interactions by month
  interactions.filter(interaction => new Date(interaction.created_at) >= last6Months)
    .forEach(interaction => {
      const date = new Date(interaction.created_at);
      const monthStr = format(date, 'MMM');
      if (monthlyCounts[monthStr] !== undefined) {
        monthlyCounts[monthStr]++;
      }
    });
  
  // Determine monthly limits based on plan (simplified for this example)
  const monthlyLimit = 30000; // This should come from the user's plan
  
  // Convert to array format
  Object.entries(monthlyCounts).forEach(([month, count]) => {
    monthlyData.push({ month, count, limit: monthlyLimit });
  });
  
  // Sort by chronological order
  monthlyData.sort((a, b) => {
    const monthsOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return monthsOrder.indexOf(a.month) - monthsOrder.indexOf(b.month);
  });
  
  return monthlyData;
}