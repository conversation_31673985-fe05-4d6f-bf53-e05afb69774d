import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

// Define type for platform data with platform-specific info
interface PlatformData {
  // Common identifiers across platforms
  platform_id: string;
  external_id: string;
  name?: string;

  // Platform-specific fields can be included as needed
  // Discord
  guild_id?: string;
  guild_name?: string;

  // Slack
  team_id?: string;
  workspace_name?: string;

  // Telegram
  chat_id?: string;
  chat_name?: string;
}

export async function POST(req: Request) {
  try {
    // Extract the API key from the Authorization header
    const authHeader = req.headers.get("Authorization") || "";
    const apiKey = authHeader.replace("Bearer ", "").trim();

    if (!apiKey) {
      return NextResponse.json(
        { valid: false, message: "No API key provided" },
        { status: 401 }
      );
    }

    // Parse request body to get service and platform data
    const body = await req.json();
    const { service, platform_data } = body;

    // Validate required environment variables are present
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing required Supabase configuration");
      return NextResponse.json(
        { valid: false, message: "Server configuration error" },
        { status: 500 }
      );
    }

    // Create a Supabase client with the service role key for admin access
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse the API key to get prefix and secret
    const keyParts = apiKey.split("_");
    if (keyParts.length < 3) {
      return NextResponse.json(
        { valid: false, message: "Invalid API key format" },
        { status: 401 }
      );
    }

    // The key format is expected to be: cmz_xx_prefix_secret
    // Extract the prefix (cmz_xx_prefix)
    const keyPrefix = keyParts.slice(0, 3).join("_");

    // Hash the full key for comparison
    const keyHash = require("crypto")
      .createHash("sha256")
      .update(apiKey)
      .digest("hex");

    // Validate the API key
    const { data: keyData, error: keyError } = await supabase
      .from("api_keys")
      .select(
        "id, name, organization_id, expires_at, revoked_at, platform, key_prefix, key_hash"
      )
      .eq("key_prefix", keyPrefix)
      .eq("key_hash", keyHash)
      .single();

      if (keyError || !keyData) {
      return NextResponse.json(
        { valid: false, message: "Invalid API key" },
        { status: 401 }
      );
    }

    // Check if the key has been revoked
    if (keyData.revoked_at) {
      return NextResponse.json(
        { valid: false, message: "API key has been revoked" },
        { status: 401 }
      );
    }

    // Check if the key has expired
    if (keyData.expires_at && new Date(keyData.expires_at) < new Date()) {
      return NextResponse.json(
        { valid: false, message: "API key has expired" },
        { status: 401 }
      );
    }

    // If platform data is provided, register/update the agent
    let agentId = null;
    let platformId = null;
    let externalId = null;

    if (platform_data) {
      // Extract platform identifiers based on the service
      if (service === "discord-agent") {
        platformId = "discord";
        externalId = platform_data.guild_id;
      } else if (service === "slack-agent") {
        platformId = "slack";
        externalId = platform_data.team_id;
      } else if (service === "telegram-agent") {
        platformId = "telegram";
        externalId = platform_data.chat_id;
      } else if (platform_data.platform_id && platform_data.external_id) {
        // Generic handling for any platform that provides explicit identifiers
        platformId = platform_data.platform_id;
        externalId = platform_data.external_id;
      }

      // Determine a name for this agent
      const agentName =
        platform_data.name ||
        platform_data.guild_name ||
        platform_data.workspace_name ||
        platform_data.chat_name ||
        `${platformId} ${externalId}`;

      if (platformId && externalId) {
        // Check if this agent already exists
        const { data: existingAgent } = await supabase
          .from("agents")
          .select("id")
          .eq("platform_id", platformId)
          .eq("external_id", externalId)
          .single();

        if (existingAgent) {
          // Update the existing agent with the new API key
          const { data: updatedAgent, error: updateError } = await supabase
            .from("agents")
            .update({
              api_key_id: keyData.id,
              organization_id: keyData.organization_id,
              name: agentName, // Update name in case it changed
              last_active_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
            .eq("id", existingAgent.id)
            .select("id")
            .single();

          if (updateError) {
            console.error(`Error updating ${platformId} agent:`, updateError);
            return NextResponse.json(
              {
                valid: false,
                message: `Failed to update agent: ${updateError.message}`,
              },
              { status: 500 }
            );
          }

          if (!updatedAgent) {
            console.error(
              `Failed to update agent due to row-level security or permissions issue`
            );
            return NextResponse.json(
              {
                valid: false,
                message: "Permission denied: Unable to update agent record",
              },
              { status: 403 }
            );
          }

          agentId = updatedAgent.id;
        } else {
          // Create a new agent for this platform entity
          const { data: newAgent, error: createError } = await supabase
            .from("agents")
            .insert({
              platform_id: platformId,
              external_id: externalId,
              name: agentName,
              api_key_id: keyData.id,
              organization_id: keyData.organization_id,
              activated_at: new Date().toISOString(),
              last_active_at: new Date().toISOString(),
              settings: { moderation_enabled: true },
            })
            .select("id")
            .single();

          if (createError) {
            console.error(`Error creating ${platformId} agent:`, createError);
            return NextResponse.json(
              {
                valid: false,
                message: `Failed to create agent: ${createError.message}`,
                organization_id: keyData.organization_id,
                platform_id: platformId,
                external_id: externalId,
                agent_id: null,
              },
              { status: 500 }
            );
          }

          if (!newAgent) {
            console.error(
              `Failed to create agent due to row-level security or permissions issue`
            );
            return NextResponse.json(
              {
                valid: false,
                message: "Permission denied: Unable to create agent record",
                organization_id: keyData.organization_id,
                platform_id: platformId,
                external_id: externalId,
                agent_id: null,
              },
              { status: 403 }
            );
          }

          agentId = newAgent.id;
        }
      }
    }

    // Update the last_used_at timestamp for the API key
    await supabase
      .from("api_keys")
      .update({ last_used_at: new Date().toISOString() })
      .eq("id", keyData.id);

    // Calculate days until expiry if the key has an expiration date
    let daysUntilExpiry: number | undefined = undefined;
    if (keyData.expires_at) {
      const expiryDate = new Date(keyData.expires_at);
      const currentDate = new Date();
      const timeDiff = expiryDate.getTime() - currentDate.getTime();
      daysUntilExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));
    }
    console.log("AGENT ID", agentId)
    // Return success with key details and ID if available
    return NextResponse.json({
      valid: true,
      message: keyData.name ? `Using API key: ${keyData.name}` : undefined,
      agent_id: agentId,
      organization_id: keyData.organization_id,
      platform_id: platformId,
      external_id: externalId,
      key_expires_at: keyData.expires_at || undefined,
      days_until_expiry: daysUntilExpiry,
    });
  } catch (error: any) {
    console.error("Error validating API key:", error);

    return NextResponse.json(
      {
        valid: false,
        message: "Server error while validating API key",
      },
      { status: 500 }
    );
  }
}
