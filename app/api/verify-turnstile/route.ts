import { NextRequest, NextResponse } from "next/server"

export async function POST(req: NextRequest) {
  const { token } = await req.json()
  if (!token) {
    return NextResponse.json({ success: false, message: "Missing Turnstile token" }, { status: 400 })
  }

  const secretKey = process.env.TURNSTILE_SECRET_KEY
  if (!secretKey) {
    return NextResponse.json({ success: false, message: "Missing Turnstile secret key" }, { status: 500 })
  }

  const formData = new URLSearchParams()
  formData.append("secret", secretKey)
  formData.append("response", token)

  const cfRes = await fetch("https://challenges.cloudflare.com/turnstile/v0/siteverify", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: formData.toString(),
  })
  const data = await cfRes.json()

  if (data.success) {
    return NextResponse.json({ success: true })
  }
  return NextResponse.json({ success: false, message: "Turnstile verification failed" }, { status: 400 })
}
