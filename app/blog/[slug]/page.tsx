import { <PERSON><PERSON><PERSON> } from "next";
import Image from "next/image";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { notFound } from "next/navigation";

// Define the type for blog post data
type BlogPost = {
  title: string;
  content: string;
  date: string;
  author: string;
  authorImage: string;
  authorTitle: string;
};

type BlogPostsData = {
  [slug: string]: BlogPost;
};

// This would typically come from a CMS or API
const blogPostsData: BlogPostsData = {
  "introducing-commuza": {
    title: "Introducing Commuza: The Future of Communication",
    content: `
    <p class="mb-4">Commuza is a revolutionary platform designed to transform the way teams communicate and collaborate. With features designed for modern workplaces, Commuza brings together messaging, file sharing, task management, and more into one seamless interface.</p>
    
    <p class="mb-4">Our mission is to simplify workplace communication while making it more effective. We believe that when communication is clear and efficient, teams can focus on what matters most: doing great work.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Key Features</h2>
    
    <ul class="list-disc pl-6 mb-6">
      <li class="mb-2">Unified messaging across departments</li>
      <li class="mb-2">Contextual file sharing with version history</li>
      <li class="mb-2">Integrated task management</li>
      <li class="mb-2">Advanced search capabilities</li>
      <li class="mb-2">Customizable workflows</li>
    </ul>
    
    <p class="mb-4">We've spent years researching the pain points in workplace communication. Our solution addresses these challenges by focusing on user experience, security, and flexibility.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Looking Forward</h2>
    
    <p class="mb-4">This is just the beginning for Commuza. Our roadmap includes AI-assisted communication tools, deeper integrations with popular workplace software, and advanced analytics to help teams optimize their communication patterns.</p>
    
    <p class="mb-4">We're excited to partner with organizations of all sizes to transform how they work together. Join us on this journey to better workplace communication.</p>
    `,
    date: "April 28, 2025",
    author: "Jane Doe",
    authorImage: "/profile-1.jpg",
    authorTitle: "CEO & Co-founder"
  },
  "improve-team-productivity": {
    title: "5 Ways to Improve Team Productivity",
    content: `<p>Content for productivity article...</p>`,
    date: "April 20, 2025",
    author: "John Smith",
    authorImage: "/profile-2.jpg",
    authorTitle: "Head of Product"
  },
  "evolution-of-workplace-communication": {
    title: "The Evolution of Workplace Communication",
    content: `<p>Content about evolution of communication...</p>`,
    date: "April 15, 2025",
    author: "Alex Johnson",
    authorImage: "/profile-3.jpg",
    authorTitle: "Communications Specialist"
  },
  "case-study-company-x": {
    title: "Case Study: How Company X Increased Efficiency by 40%",
    content: `<p>Content for the case study...</p>`,
    date: "April 10, 2025",
    author: "Sarah Williams",
    authorImage: "/profile-4.jpg",
    authorTitle: "Customer Success Manager"
  },
  "upcoming-features": {
    title: "Upcoming Features in Commuza",
    content: `<p>Content about upcoming features...</p>`,
    date: "April 5, 2025",
    author: "Michael Brown",
    authorImage: "/profile-5.jpg",
    authorTitle: "Lead Developer"
  }
};

type Props = {
  params: {
    slug: string;
  };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const post = blogPostsData[params.slug];
  
  if (!post) {
    return {
      title: "Post Not Found | Commuza Blog"
    };
  }
  
  return {
    title: `${post.title} | Commuza Blog`,
    description: post.content.substring(0, 160).replace(/<[^>]*>?/gm, ''),
  };
}

export default function BlogPostPage({ params }: Props) {
  const post = blogPostsData[params.slug];
  
  if (!post) {
    notFound();
  }
  
  return (
    <div className="container max-w-4xl mx-auto py-12">
      <div className="mb-8">
        <Link href="/blog">
          <Button variant="ghost" size="sm" className="gap-2 mb-4">
            <ArrowLeft className="h-4 w-4" />
            Back to all posts
          </Button>
        </Link>
        <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
        
        <div className="flex items-center gap-4 mb-8">
          <div className="h-12 w-12 rounded-full overflow-hidden relative">
            <Image 
              src={post.authorImage}
              alt={post.author}
              fill
              className="object-cover"
            />
          </div>
          <div>
            <div className="font-medium">{post.author}</div>
            <div className="text-sm text-muted-foreground">{post.authorTitle}</div>
            <div className="text-sm text-muted-foreground">{post.date}</div>
          </div>
        </div>
      </div>
      
      <div className="prose dark:prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: post.content }} />
    </div>
  );
}