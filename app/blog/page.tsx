import { Metadata } from "next";
import { BlogPostCard } from "@/components/blog/BlogPostCard";

export const metadata: Metadata = {
  title: "Blog | Commuza",
  description: "Latest news, updates, and insights from the <PERSON><PERSON>uza team",
};

// This would typically come from a CMS or API
const blogPosts = [
  {
    title: "Introducing Commuza: The Future of Communication",
    summary: "Learn about how Commuza is revolutionizing the way teams communicate and collaborate.",
    date: "April 28, 2025",
    author: "<PERSON>",
    authorImage: "/profile-1.jpg",
    href: "/blog/introducing-commuza",
  },
  {
    title: "5 Ways to Improve Team Productivity",
    summary: "Discover proven strategies to boost your team's efficiency and productivity with Commuza.",
    date: "April 20, 2025",
    author: "<PERSON>",
    authorImage: "/profile-2.jpg",
    href: "/blog/improve-team-productivity",
  },
  {
    title: "The Evolution of Workplace Communication",
    summary: "A deep dive into how workplace communication has evolved and where it's headed.",
    date: "April 15, 2025",
    author: "<PERSON>",
    authorImage: "/profile-3.jpg",
    href: "/blog/evolution-of-workplace-communication",
  },
  {
    title: "Case Study: How Company X Increased Efficiency by 40%",
    summary: "Read how Company X transformed their communication strategy with Commuza.",
    date: "April 10, 2025",
    author: "<PERSON> Williams",
    authorImage: "/profile-4.jpg",
    href: "/blog/case-study-company-x",
  },
  {
    title: "Upcoming Features in Commuza",
    summary: "Get a sneak peek at the exciting new features we're developing for Commuza.",
    date: "April 5, 2025",
    author: "Michael Brown",
    authorImage: "/profile-5.jpg",
    href: "/blog/upcoming-features",
  },
];

export default function BlogPage() {
  return (
    <div className="container max-w-6xl mx-auto py-12">
      <div className="space-y-4 mb-8">
        <h1 className="text-4xl font-bold">Blog</h1>
        <p className="text-xl text-muted-foreground">
          Latest news, updates, and insights from the Commuza team
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {blogPosts.map((post, index) => (
          <BlogPostCard
            key={index}
            title={post.title}
            summary={post.summary}
            date={post.date}
            author={post.author}
            authorImage={post.authorImage}
            href={post.href}
          />
        ))}
      </div>
    </div>
  );
}