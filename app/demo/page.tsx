"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { ThemeAwareLogo } from "@/components/ui/theme-aware-logo"
import { Turnstile } from '@marsidev/react-turnstile'

export default function DemoRequestPage() {
  const [captchaToken, setCaptchaToken] = useState("");
  const [form, setForm] = useState({
    name: "",
    email: "",
    company: "",
    message: "",
  })
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)

  function validate() {
    if (!form.name.trim()) return "Name is required."
    if (!form.email.trim()) return "Email is required."
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email)) return "Enter a valid email address."
    return ""
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) {
    setForm({ ...form, [e.target.name]: e.target.value })
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setError("")
    const err = validate()
    if (err) {
      setError(err)
      return
    }
    if (!captchaToken) {
      setError("Please complete the Turnstile challenge.")
      return
    }
    setLoading(true)
    // Verify Turnstile
    const res = await fetch('/api/verify-turnstile', {
      method: 'POST',
      body: JSON.stringify({ token: captchaToken }),
      headers: {
        'content-type': 'application/json'
      }
    })
    const data = await res.json()
    if (!data.success) {
      setError("Turnstile verification failed. Please try again.")
      setLoading(false)
      return
    }
    setTimeout(() => {
      setSubmitted(true)
      setLoading(false)
    }, 1200)
  }

  if (submitted) {
    return (
      <div className="max-w-lg mx-auto py-16 px-4">
        <div className="flex flex-col items-center">
          <ThemeAwareLogo size="lg" className="mb-4" />
          <h1 className="text-2xl font-bold mb-2">Thank you for requesting a demo!</h1>
          <p className="text-muted-foreground mb-8 text-center">We'll be in touch soon to schedule your session.</p>
          <Button onClick={() => setSubmitted(false)} variant="outline">Request another</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-[80vh] flex items-center justify-center bg-background py-12 px-2">
      <div className="w-full max-w-3xl bg-card rounded-2xl shadow-xl border flex flex-col md:flex-row overflow-hidden">
        {/* Left: Logo & Company Info */}
        <div className="md:w-1/2 flex flex-col items-center justify-center bg-muted/50 px-6 py-8 md:py-12 border-b md:border-b-0 md:border-r">
          <ThemeAwareLogo size="lg" className="mb-4" />
          <p className="text-muted-foreground mb-4 text-center">
            AI-powered community managers for modern brands and communities.
          </p>
          <div className="text-sm text-muted-foreground text-center mb-2">
            <span className="font-semibold">Mission:</span> Empower communities with intelligent, automated engagement and moderation.
          </div>
          <div className="text-xs text-muted-foreground text-center">
            <span className="font-semibold">Contact:</span> <a href="mailto:<EMAIL>" className="underline"><EMAIL></a>
          </div>
        </div>
        {/* Right: Demo Request Form */}
        <div className="md:w-1/2 flex flex-col justify-center px-6 py-8 md:py-12">
          <h1 className="text-2xl font-bold mb-6 text-center">Request a Demo</h1>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="name">Name <span className="text-destructive">*</span></Label>
              <Input id="name" name="name" value={form.name} onChange={handleChange} required disabled={loading} autoComplete="name" />
            </div>
            <div>
              <Label htmlFor="email">Email <span className="text-destructive">*</span></Label>
              <Input id="email" name="email" type="email" value={form.email} onChange={handleChange} required disabled={loading} autoComplete="email" />
            </div>
            <div>
              <Label htmlFor="company">Company</Label>
              <Input id="company" name="company" value={form.company} onChange={handleChange} disabled={loading} autoComplete="organization" />
            </div>
            <div>
              <Label htmlFor="message">Message</Label>
              <textarea id="message" name="message" value={form.message} onChange={handleChange} className="w-full min-h-[80px] rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:opacity-50" disabled={loading} />
            </div>
            {error && <div className="text-destructive text-sm">{error}</div>}
            <Turnstile 
              siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY}
              onSuccess={setCaptchaToken}
              className="w-full"
              style={{ width: '100%' }}
            />
            <Button type="submit" className="w-full" disabled={loading}>{loading ? "Submitting..." : "Request Demo"}</Button>
          </form>
        </div>
      </div>
    </div>
  )
}

