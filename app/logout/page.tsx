"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LogoLoading } from "@/components/ui/logo-loading"
import { createClient } from "@/lib/supabase/client"

export default function LogoutPage() {
  const router = useRouter()
  
  useEffect(() => {
    const performLogout = async () => {
      try {
        // Create supabase client inside effect to avoid rendering issues
        const supabase = createClient()
        
        // First sign out from client-side
        await supabase.auth.signOut()
        
        // Then call server-side logout to handle cookies and complete session cleanup
        const response = await fetch("/api/auth/logout", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        })
        
        if (!response.ok) {
          throw new Error(`Server logout failed: ${response.statusText}`)
        }
        
        // Let the server handle redirection after successful logout
        window.location.href = "/login"
      } catch (error) {
        console.error("Logout error:", error)
        // Fallback redirect if server-side fails
        router.push("/login")
      }
    }

    performLogout()
  }, [router])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background">
      <LogoLoading size="lg" />
      <div className="mt-8 text-xl font-bold">Logging out...</div>
      <p className="mt-2 text-muted-foreground">Please wait while we securely log you out.</p>
    </div>
  )
}