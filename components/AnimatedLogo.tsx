"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface AnimatedLogoProps {
  size?: "sm" | "md" | "lg"
  hideOutsideLabel?: boolean
  className?: string
  href?: string
}

export function AnimatedLogo({
  size = "md",
  hideOutsideLabel = false,
  className,
  href = "/dashboard",
}: AnimatedLogoProps) {
  const sizes = {
    sm: { logo: 32, height: 32, textSize: "text-lg" },
    md: { logo: 40, height: 40, textSize: "text-xl" },
    lg: { logo: 48, height: 48, textSize: "text-2xl" },
  }

  const { logo, height, textSize } = sizes[size]

  return (
    <Link href={href} className={cn("flex items-center gap-2", className)}>
      <motion.div
        className="relative shrink-0"
        style={{ height, width: logo }}
        whileHover={{ scale: 1.05 }}
        animate={{ y: [0, -5, 0] }}
        transition={{
          duration: 3,
          repeat: Number.POSITIVE_INFINITY,
          repeatType: "loop",
          ease: "easeInOut",
        }}
      >
        <Image src="/logo.svg" alt="Commuza Logo" fill priority className="object-contain" />
      </motion.div>
      {!hideOutsideLabel && (
        <motion.span
          className={cn("font-bold", textSize)}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.2 }}
        >
          Commuza
        </motion.span>
      )}
    </Link>
  )
}
