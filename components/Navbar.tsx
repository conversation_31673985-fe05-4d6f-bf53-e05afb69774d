"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Bell, Menu, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ThemeAwareLogo } from "@/components/ui/theme-aware-logo"
import { ThemeToggle } from "@/components/theme-toggle"
import { useAuth } from "@/components/auth/auth-provider"
import { useSidebar } from "@/hooks/use-sidebar"
import { LogoutButton } from "@/components/auth/logout-button"

export function Navbar() {
  const { user, profile } = useAuth()
  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { toggleSidebar } = useSidebar()

  // Function to get user initials for avatar fallback
  const getUserInitials = () => {
    if (profile?.full_name) {
      return profile.full_name
        .split(" ")
        .map((name) => name[0])
        .join("")
        .toUpperCase()
        .substring(0, 2)
    }
    return user?.email?.substring(0, 2).toUpperCase() || "U"
  }

  // Function to get page title from pathname
  const getPageTitle = () => {
    if (pathname === "/dashboard") return "Dashboard"

    const segments = pathname.split("/").filter(Boolean)
    if (segments.length >= 2) {
      // Convert kebab-case to Title Case
      return segments[1]
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    }

    return "Dashboard"
  }

  // Mock notifications
  const notifications = [
    { id: 1, title: "New conversation started", time: "5 minutes ago", read: false },
    { id: 2, title: "Weekly report available", time: "1 hour ago", read: false },
    { id: 3, title: "System update completed", time: "Yesterday", read: true },
  ]

  return (
    <header className="sticky top-0 z-50 h-navbar w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-full items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-4">
          <ThemeAwareLogo href="/dashboard" />

          {/* Mobile menu button */}
          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="icon" aria-label="Menu">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[300px] sm:w-[350px]">
              <div className="flex flex-col gap-6 py-4">
                <ThemeAwareLogo />
                <nav className="flex flex-col gap-2">
                  <Link
                    href="/dashboard"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/dashboard/ai-manager"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    AI Manager
                  </Link>
                  <Link
                    href="/dashboard/conversations"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    Conversations
                  </Link>
                  <Link
                    href="/dashboard/notifications"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    Notifications
                  </Link>
                  <Link
                    href="/dashboard/storage"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    Storage
                  </Link>
                  <Link
                    href="/dashboard/billing"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    Billing
                  </Link>
                  <Link
                    href="/dashboard/settings"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent"
                  >
                    Settings
                  </Link>
                </nav>
              </div>
            </SheetContent>
          </Sheet>

          {/* Sidebar toggle button (tablet and up) */}
          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        {/* Center: Page title breadcrumb (lg screens only) */}
        <div className="hidden lg:block">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <div>
                  <Link href="/dashboard" className="text-sm font-medium text-muted-foreground hover:text-foreground">
                    Dashboard
                  </Link>
                </div>
              </li>
              {pathname !== "/dashboard" && (
                <>
                  <li>
                    <div className="flex items-center">
                      <span className="mx-2 text-muted-foreground">/</span>
                      <span className="text-sm font-medium">{getPageTitle()}</span>
                    </div>
                  </li>
                </>
              )}
            </ol>
          </nav>
        </div>

        {/* Right side: links and actions */}
        <div className="flex items-center gap-2">
          <div className="hidden md:flex items-center gap-4 mr-2">
            <Link
              href="https://docs.commuza.io"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-muted-foreground hover:text-foreground flex items-center gap-1"
            >
              Docs <ExternalLink className="h-3 w-3" />
            </Link>
            <Link
              href="https://status.commuza.io"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-muted-foreground hover:text-foreground flex items-center gap-1"
            >
              Status <ExternalLink className="h-3 w-3" />
            </Link>
          </div>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Notifications dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                  {notifications.filter((n) => !n.read).length}
                </span>
                <span className="sr-only">Notifications</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <AnimatePresence>
                {notifications.map((notification) => (
                  <motion.div
                    key={notification.id}
                    initial={{ opacity: 0, y: -8 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -8 }}
                    transition={{ duration: 0.15 }}
                  >
                    <DropdownMenuItem className="cursor-pointer flex items-start gap-2 p-3">
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium">{notification.title}</p>
                          {!notification.read && <Badge variant="default" className="h-1.5 w-1.5 rounded-full p-0" />}
                        </div>
                        <p className="text-xs text-muted-foreground">{notification.time}</p>
                      </div>
                    </DropdownMenuItem>
                  </motion.div>
                ))}
              </AnimatePresence>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="cursor-pointer justify-center text-sm text-muted-foreground">
                View all notifications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User profile dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={profile?.avatar_url || ""} alt={profile?.full_name || user?.email || "User"} />
                  <AvatarFallback>{getUserInitials()}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">{profile?.full_name || "User"}</p>
                  <p className="text-xs text-muted-foreground">{user?.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/dashboard/settings/profile">Profile</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/settings">Settings</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/billing">Billing</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <LogoutButton variant="ghost" className="w-full justify-start px-2" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
