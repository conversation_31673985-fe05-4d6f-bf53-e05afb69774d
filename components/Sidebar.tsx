"use client"

import type React from "react"

import { useEffect } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, BrainCog, MessageSquare, Bell, Folder, CreditCard, Settings } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebar } from "@/hooks/use-sidebar"
import { useAuth } from "@/components/auth/auth-provider"
import { LogoutButton } from "@/components/auth/logout-button"

interface SidebarItemProps {
  icon: React.ElementType
  label: string
  href: string
  active?: boolean
  collapsed?: boolean
}

function SidebarItem({ icon: Icon, label, href, active, collapsed }: SidebarItemProps) {
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link href={href} passHref>
            <Button
              variant="ghost"
              size="lg"
              className={cn(
                "w-full justify-start gap-3 px-3",
                active ? "bg-accent text-accent-foreground" : "text-muted-foreground hover:text-foreground",
                collapsed && "justify-center px-0",
              )}
            >
              <Icon className={cn("h-5 w-5 shrink-0", active && "text-primary")} />
              {!collapsed && <span>{label}</span>}
            </Button>
          </Link>
        </TooltipTrigger>
        {collapsed && <TooltipContent side="right">{label}</TooltipContent>}
      </Tooltip>
    </TooltipProvider>
  )
}

export function Sidebar() {
  const pathname = usePathname()
  const { collapsed, setCollapsed } = useSidebar()
  const { signOut } = useAuth()

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setCollapsed(true)
      }
    }

    window.addEventListener("resize", handleResize)
    handleResize()

    return () => window.removeEventListener("resize", handleResize)
  }, [setCollapsed])

  const sidebarItems = [
    { icon: Home, label: "Dashboard", href: "/dashboard" },
    { icon: BrainCog, label: "AI Manager", href: "/dashboard/ai-manager" },
    { icon: MessageSquare, label: "Conversations", href: "/dashboard/conversations" },
    { icon: Bell, label: "Notifications", href: "/dashboard/notifications" },
    { icon: Folder, label: "Storage", href: "/dashboard/storage" },
    { icon: CreditCard, label: "Billing", href: "/dashboard/billing" },
    { icon: Settings, label: "Settings", href: "/dashboard/settings" },
  ]

  return (
    <motion.aside
      className="fixed left-0 z-30 flex h-[calc(100vh-80px)] flex-col border-r bg-background"
      initial={{ width: collapsed ? 80 : 250 }}
      animate={{ width: collapsed ? 80 : 250 }}
      transition={{ duration: 0.2, ease: [0.33, 1, 0.68, 1] }}
    >
      <div className="flex flex-col gap-2 p-4">
        {sidebarItems.map((item) => (
          <SidebarItem
            key={item.href}
            icon={item.icon}
            label={item.label}
            href={item.href}
            active={pathname === item.href || pathname.startsWith(`${item.href}/`)}
            collapsed={collapsed}
          />
        ))}
      </div>

      <div className="mt-auto p-4">
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <LogoutButton
                variant="ghost"
                size="lg"
                className={cn(
                  "w-full justify-start gap-3 px-3 text-muted-foreground hover:text-foreground",
                  collapsed && "justify-center px-0",
                )}
                showText={!collapsed}
              />
            </TooltipTrigger>
            {collapsed && <TooltipContent side="right">Log out</TooltipContent>}
          </Tooltip>
        </TooltipProvider>
      </div>
    </motion.aside>
  )
}
