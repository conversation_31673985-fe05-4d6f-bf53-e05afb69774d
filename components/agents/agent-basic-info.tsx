"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface AgentBasicInfoProps {
  data: any
  updateData: (data: any) => void
}

export function AgentBasicInfo({ data, updateData }: AgentBasicInfoProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Basic Information</h2>
        <p className="text-muted-foreground">Provide basic details about your AI agent</p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Agent Name</Label>
          <Input
            id="name"
            placeholder="e.g., Community Assistant"
            value={data.name}
            onChange={(e) => updateData({ name: e.target.value })}
          />
          <p className="text-sm text-muted-foreground">This is how your agent will be identified in your dashboard</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Describe what this agent does..."
            value={data.description}
            onChange={(e) => updateData({ description: e.target.value })}
            rows={4}
          />
          <p className="text-sm text-muted-foreground">A brief description of your agent's purpose and capabilities</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="role">Primary Role</Label>
          <Select value={data.role} onValueChange={(value) => updateData({ role: value })}>
            <SelectTrigger id="role">
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="community-manager">Community Manager</SelectItem>
              <SelectItem value="moderator">Moderator</SelectItem>
              <SelectItem value="support-agent">Support Agent</SelectItem>
              <SelectItem value="engagement-specialist">Engagement Specialist</SelectItem>
              <SelectItem value="growth-advisor">Growth Advisor</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">The primary function your agent will serve in your community</p>
        </div>
      </div>
    </div>
  )
}
