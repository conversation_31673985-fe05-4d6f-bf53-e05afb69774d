"use client"

import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { useState } from "react"

interface AgentIntegrationsProps {
  data: any
  updateData: (data: any) => void
}

const platforms = [
  {
    id: "discord",
    name: "Discord",
    icon: "🎮",
    description: "Connect to Discord servers and channels",
  },
  {
    id: "slack",
    name: "Slack",
    icon: "💬",
    description: "Integrate with Slack workspaces",
  },
  {
    id: "telegram",
    name: "Telegram",
    icon: "📱",
    description: "Connect to Telegram groups and channels",
  },
  {
    id: "reddit",
    name: "Reddit",
    icon: "🔍",
    description: "Monitor and respond to Reddit communities",
  },
]

export function AgentIntegrations({ data, updateData }: AgentIntegrationsProps) {
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null)

  const handleSelectPlatform = (platformId: string) => {
    setSelectedPlatform(platformId)
  }

  const handleAddIntegration = () => {
    // In a real app, you would validate and process the integration
    // For now, we'll just add the platform to the list
    if (selectedPlatform && !data.integrations.includes(selectedPlatform)) {
      updateData({
        integrations: [...data.integrations, selectedPlatform],
      })
    }
    setSelectedPlatform(null)
  }

  const handleRemoveIntegration = (platformId: string) => {
    updateData({
      integrations: data.integrations.filter((id: string) => id !== platformId),
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Platform Integrations</h2>
        <p className="text-muted-foreground">Connect your AI agent to the platforms where your community lives</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <Label>Current Integrations</Label>
          {data.integrations.length === 0 ? (
            <p className="text-sm text-muted-foreground">No integrations added yet. Add a platform below.</p>
          ) : (
            <div className="space-y-2">
              {data.integrations.map((platformId: string) => {
                const platform = platforms.find((p) => p.id === platformId)
                if (!platform) return null
                return (
                  <div key={platform.id} className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{platform.icon}</div>
                      <div>
                        <h3 className="font-medium">{platform.name}</h3>
                        <p className="text-sm text-muted-foreground">Connected</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => handleRemoveIntegration(platform.id)}>
                      Remove
                    </Button>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        <div className="space-y-4">
          <Label>Add New Integration</Label>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            {platforms.map((platform) => (
              <Card
                key={platform.id}
                className={`cursor-pointer transition-colors ${
                  selectedPlatform === platform.id ? "border-primary" : "hover:border-primary/50"
                } ${data.integrations.includes(platform.id) ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={() => {
                  if (!data.integrations.includes(platform.id)) {
                    handleSelectPlatform(platform.id)
                  }
                }}
              >
                <CardContent className="flex items-center space-x-4 p-4">
                  <div className="text-3xl">{platform.icon}</div>
                  <div className="flex-1">
                    <h3 className="font-medium">{platform.name}</h3>
                    <p className="text-sm text-muted-foreground">{platform.description}</p>
                  </div>
                  <Checkbox
                    checked={selectedPlatform === platform.id}
                    disabled={data.integrations.includes(platform.id)}
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {selectedPlatform && (
          <div className="space-y-4">
            <div className="rounded-lg border p-4">
              <h3 className="font-medium mb-4">
                Configure {platforms.find((p) => p.id === selectedPlatform)?.name} Integration
              </h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="api-key">API Key</Label>
                  <Input id="api-key" placeholder="Enter your API key" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="webhook">Webhook URL (Optional)</Label>
                  <Input id="webhook" placeholder="https://..." />
                </div>
                <Button onClick={handleAddIntegration}>Add Integration</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
