"use client"

import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { useState } from "react"

interface AgentPersonalityProps {
  data: any
  updateData: (data: any) => void
}

export function AgentPersonality({ data, updateData }: AgentPersonalityProps) {
  const [formality, setFormality] = useState(50)
  const [humor, setHumor] = useState(30)
  const [verbosity, setVerbosity] = useState(40)

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Personality & Tone</h2>
        <p className="text-muted-foreground">Define how your AI agent will communicate with your community</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <Label>Communication Tone</Label>
          <RadioGroup
            value={data.tone}
            onValueChange={(value) => updateData({ tone: value })}
            className="grid grid-cols-2 gap-4 sm:grid-cols-4"
          >
            <div>
              <RadioGroupItem value="friendly" id="friendly" className="peer sr-only" />
              <Label
                htmlFor="friendly"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span className="text-center font-medium">Friendly</span>
                <span className="text-center text-xs text-muted-foreground">Warm and approachable</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem value="professional" id="professional" className="peer sr-only" />
              <Label
                htmlFor="professional"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span className="text-center font-medium">Professional</span>
                <span className="text-center text-xs text-muted-foreground">Formal and business-like</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem value="casual" id="casual" className="peer sr-only" />
              <Label
                htmlFor="casual"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span className="text-center font-medium">Casual</span>
                <span className="text-center text-xs text-muted-foreground">Relaxed and conversational</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem value="enthusiastic" id="enthusiastic" className="peer sr-only" />
              <Label
                htmlFor="enthusiastic"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span className="text-center font-medium">Enthusiastic</span>
                <span className="text-center text-xs text-muted-foreground">Energetic and positive</span>
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label>Formality Level</Label>
              <span className="text-sm text-muted-foreground">
                {formality < 30 ? "Very Casual" : formality < 60 ? "Balanced" : "Very Formal"}
              </span>
            </div>
            <Slider value={[formality]} min={0} max={100} step={1} onValueChange={(value) => setFormality(value[0])} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <Label>Humor Level</Label>
              <span className="text-sm text-muted-foreground">
                {humor < 30 ? "Serious" : humor < 60 ? "Occasional Humor" : "Very Humorous"}
              </span>
            </div>
            <Slider value={[humor]} min={0} max={100} step={1} onValueChange={(value) => setHumor(value[0])} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <Label>Response Length</Label>
              <span className="text-sm text-muted-foreground">
                {verbosity < 30 ? "Concise" : verbosity < 60 ? "Balanced" : "Detailed"}
              </span>
            </div>
            <Slider value={[verbosity]} min={0} max={100} step={1} onValueChange={(value) => setVerbosity(value[0])} />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="personality">Custom Personality Instructions</Label>
          <Textarea
            id="personality"
            placeholder="Add specific instructions about how your agent should behave..."
            value={data.personality}
            onChange={(e) => updateData({ personality: e.target.value })}
            rows={4}
          />
          <p className="text-sm text-muted-foreground">
            Additional details about your agent's personality, communication style, or behavior
          </p>
        </div>
      </div>
    </div>
  )
}
