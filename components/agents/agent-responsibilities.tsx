"use client"

import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"

interface AgentResponsibilitiesProps {
  data: any
  updateData: (data: any) => void
}

export function AgentResponsibilities({ data, updateData }: AgentResponsibilitiesProps) {
  const [activeTab, setActiveTab] = useState("engagement")

  const updateResponsibility = (key: string, value: boolean) => {
    updateData({
      responsibilities: {
        ...data.responsibilities,
        [key]: value,
      },
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Agent Responsibilities</h2>
        <p className="text-muted-foreground">Define what your AI agent will be responsible for in your community</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="engagement">Community Engagement</Label>
              <p className="text-sm text-muted-foreground">
                Respond to questions, welcome new members, and foster discussions
              </p>
            </div>
            <Switch
              id="engagement"
              checked={data.responsibilities.engagement}
              onCheckedChange={(checked) => updateResponsibility("engagement", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="moderation">Content Moderation</Label>
              <p className="text-sm text-muted-foreground">Flag inappropriate content, enforce community guidelines</p>
            </div>
            <Switch
              id="moderation"
              checked={data.responsibilities.moderation}
              onCheckedChange={(checked) => updateResponsibility("moderation", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="support">User Support</Label>
              <p className="text-sm text-muted-foreground">Answer FAQs, provide help with common issues</p>
            </div>
            <Switch
              id="support"
              checked={data.responsibilities.support}
              onCheckedChange={(checked) => updateResponsibility("support", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="growth">Community Growth</Label>
              <p className="text-sm text-muted-foreground">Suggest content ideas, events, and engagement strategies</p>
            </div>
            <Switch
              id="growth"
              checked={data.responsibilities.growth}
              onCheckedChange={(checked) => updateResponsibility("growth", checked)}
            />
          </div>
        </div>

        <div className="space-y-4">
          <Label>Configure Responsibility Details</Label>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="engagement">Engagement</TabsTrigger>
              <TabsTrigger value="moderation">Moderation</TabsTrigger>
              <TabsTrigger value="support">Support</TabsTrigger>
              <TabsTrigger value="growth">Growth</TabsTrigger>
            </TabsList>
            <TabsContent value="engagement" className="space-y-4 pt-4">
              <Textarea
                placeholder="Add specific instructions for how your agent should engage with the community..."
                rows={4}
              />
              <p className="text-sm text-muted-foreground">
                Examples: "Greet new members with a welcome message", "Answer questions about our product"
              </p>
            </TabsContent>
            <TabsContent value="moderation" className="space-y-4 pt-4">
              <Textarea placeholder="Add specific instructions for content moderation..." rows={4} />
              <p className="text-sm text-muted-foreground">
                Examples: "Flag messages containing profanity", "Remove spam content"
              </p>
            </TabsContent>
            <TabsContent value="support" className="space-y-4 pt-4">
              <Textarea placeholder="Add specific instructions for user support..." rows={4} />
              <p className="text-sm text-muted-foreground">
                Examples: "Answer FAQs about pricing", "Help users troubleshoot common issues"
              </p>
            </TabsContent>
            <TabsContent value="growth" className="space-y-4 pt-4">
              <Textarea placeholder="Add specific instructions for community growth..." rows={4} />
              <p className="text-sm text-muted-foreground">
                Examples: "Suggest weekly discussion topics", "Recommend content ideas"
              </p>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
