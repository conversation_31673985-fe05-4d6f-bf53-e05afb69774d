"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, AlertCircle } from "lucide-react"

interface AgentReviewProps {
  data: any
}

export function AgentReview({ data }: AgentReviewProps) {
  // Helper function to get platform name from ID
  const getPlatformName = (id: string) => {
    const platforms: Record<string, string> = {
      discord: "Discord",
      slack: "Slack",
      telegram: "Telegram",
      reddit: "Reddit",
    }
    return platforms[id] || id
  }

  // Helper function to get role name from ID
  const getRoleName = (id: string) => {
    const roles: Record<string, string> = {
      "community-manager": "Community Manager",
      moderator: "Moderator",
      "support-agent": "Support Agent",
      "engagement-specialist": "Engagement Specialist",
      "growth-advisor": "Growth Advisor",
    }
    return roles[id] || id
  }

  // Helper function to get tone name from ID
  const getToneName = (id: string) => {
    const tones: Record<string, string> = {
      friendly: "Friendly",
      professional: "Professional",
      casual: "Casual",
      enthusiastic: "Enthusiastic",
    }
    return tones[id] || id
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Review Your AI Agent</h2>
        <p className="text-muted-foreground">Review your agent configuration before creating it</p>
      </div>

      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Basic Information</h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Agent Name</p>
                    <p className="text-base">
                      {data.name || <span className="text-muted-foreground italic">Not specified</span>}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Primary Role</p>
                    <p className="text-base">
                      {data.role ? (
                        getRoleName(data.role)
                      ) : (
                        <span className="text-muted-foreground italic">Not specified</span>
                      )}
                    </p>
                  </div>
                  <div className="sm:col-span-2">
                    <p className="text-sm font-medium text-muted-foreground">Description</p>
                    <p className="text-base">
                      {data.description || <span className="text-muted-foreground italic">Not specified</span>}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Personality & Tone</h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Communication Tone</p>
                    <p className="text-base">
                      {data.tone ? (
                        getToneName(data.tone)
                      ) : (
                        <span className="text-muted-foreground italic">Not specified</span>
                      )}
                    </p>
                  </div>
                  <div className="sm:col-span-2">
                    <p className="text-sm font-medium text-muted-foreground">Custom Personality Instructions</p>
                    <p className="text-base">
                      {data.personality || <span className="text-muted-foreground italic">Not specified</span>}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Responsibilities</h3>
                <div className="flex flex-wrap gap-2">
                  {data.responsibilities.engagement && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Check className="h-3 w-3" /> Community Engagement
                    </Badge>
                  )}
                  {data.responsibilities.moderation && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Check className="h-3 w-3" /> Content Moderation
                    </Badge>
                  )}
                  {data.responsibilities.support && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Check className="h-3 w-3" /> User Support
                    </Badge>
                  )}
                  {data.responsibilities.growth && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Check className="h-3 w-3" /> Community Growth
                    </Badge>
                  )}
                  {!Object.values(data.responsibilities).some(Boolean) && (
                    <span className="text-muted-foreground italic">No responsibilities selected</span>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Platform Integrations</h3>
                <div className="flex flex-wrap gap-2">
                  {data.integrations.length > 0 ? (
                    data.integrations.map((integration: string) => (
                      <Badge key={integration} variant="outline">
                        {getPlatformName(integration)}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground italic">No integrations selected</span>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-500" />
            <div>
              <h3 className="font-medium text-yellow-600 dark:text-yellow-500">Before you create your agent</h3>
              <div className="mt-1 text-sm text-yellow-700 dark:text-yellow-400">
                <p>
                  Please review all the information above to ensure it's correct. Once created, your agent will be ready
                  to deploy to your selected platforms after final configuration.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
