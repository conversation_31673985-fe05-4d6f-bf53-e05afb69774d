"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { AgentBasicInfo } from "./agent-basic-info"
import { AgentPersonality } from "./agent-personality"
import { AgentResponsibilities } from "./agent-responsibilities"
import { AgentIntegrations } from "./agent-integrations"
import { AgentReview } from "./agent-review"
import { Check, ArrowRight, ArrowLeft } from "lucide-react"
import { cn } from "@/lib/utils"

const steps = [
  { id: "basic-info", name: "Basic Info" },
  { id: "personality", name: "Personality" },
  { id: "responsibilities", name: "Responsibilities" },
  { id: "integrations", name: "Integrations" },
  { id: "review", name: "Review" },
]

export function AgentSetupWizard() {
  const [currentStep, setCurrentStep] = useState(0)
  const [agentData, setAgentData] = useState({
    name: "",
    description: "",
    role: "community-manager",
    tone: "friendly",
    personality: "",
    responsibilities: {
      engagement: true,
      moderation: true,
      support: false,
      growth: false,
    },
    integrations: [],
  })

  const updateAgentData = (data: Partial<typeof agentData>) => {
    setAgentData((prev) => ({ ...prev, ...data }))
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    // Here you would submit the agent data to your API
    console.log("Submitting agent data:", agentData)
    // Redirect to the agents list page after successful creation
    // router.push("/dashboard/agents");
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-center">
        <nav aria-label="Progress" className="w-full max-w-4xl">
          <ol className="flex items-center">
            {steps.map((step, index) => (
              <li key={step.id} className={cn("relative flex-1", index !== steps.length - 1 && "pr-8")}>
                <div className="flex items-center text-sm">
                  <div
                    className={cn(
                      "flex h-8 w-8 items-center justify-center rounded-full border-2",
                      currentStep > index
                        ? "border-primary bg-primary text-primary-foreground"
                        : currentStep === index
                          ? "border-primary text-primary"
                          : "border-muted-foreground/30 text-muted-foreground/30",
                    )}
                  >
                    {currentStep > index ? <Check className="h-4 w-4" /> : <span>{index + 1}</span>}
                  </div>
                  <span
                    className={cn(
                      "ml-3 text-sm font-medium",
                      currentStep >= index ? "text-foreground" : "text-muted-foreground/30",
                    )}
                  >
                    {step.name}
                  </span>
                </div>
                {index !== steps.length - 1 && (
                  <div
                    className={cn(
                      "absolute left-0 top-4 h-0.5 w-full",
                      index < currentStep ? "bg-primary" : "bg-muted-foreground/30",
                    )}
                  />
                )}
              </li>
            ))}
          </ol>
        </nav>
      </div>

      <Card>
        <CardContent className="pt-6">
          <Tabs value={steps[currentStep].id} className="w-full">
            <TabsContent value="basic-info" className="mt-0">
              <AgentBasicInfo data={agentData} updateData={updateAgentData} />
            </TabsContent>
            <TabsContent value="personality" className="mt-0">
              <AgentPersonality data={agentData} updateData={updateAgentData} />
            </TabsContent>
            <TabsContent value="responsibilities" className="mt-0">
              <AgentResponsibilities data={agentData} updateData={updateAgentData} />
            </TabsContent>
            <TabsContent value="integrations" className="mt-0">
              <AgentIntegrations data={agentData} updateData={updateAgentData} />
            </TabsContent>
            <TabsContent value="review" className="mt-0">
              <AgentReview data={agentData} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={prevStep} disabled={currentStep === 0}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Previous
        </Button>
        {currentStep === steps.length - 1 ? (
          <Button onClick={handleSubmit}>Create Agent</Button>
        ) : (
          <Button onClick={nextStep}>
            Next <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}
