'use client'

import { useToast } from "@/hooks/use-toast";
import { createClient } from "@/lib/supabase/client";
import type { Session, User } from "@supabase/supabase-js";
import { usePathname, useRouter } from "next/navigation";
import type React from "react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";

export type SupabaseClient = ReturnType<typeof createClient>;

// Types for our RBAC system
export type UserRole = "free" | "pro" | "enterprise";

export interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  role: UserRole;
  created_at: string;
}

interface AuthContextProps {
  user: User | null;
  profile: UserProfile | null;
  isLoading: boolean;
  signUp: (
    email: string,
    password: string,
    fullName: string,
    organizationName: string, // Added organizationName
    captchaToken: string
  ) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signInWithProvider: (provider: "google" | "github") => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  updatePassword: (password: string) => Promise<{ error: any }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  hasRole: (roles: UserRole[]) => boolean;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams =
    typeof window !== "undefined"
      ? new URLSearchParams(window.location.search)
      : null;
  const redirectTo = searchParams?.get("redirect") || "/dashboard";
  const { toast } = useToast();
  const supabase = useMemo(() => createClient(), []);
  // Function to fetch user profile
  const fetchProfile = useCallback(
    async (userId: string): Promise<UserProfile | null> => {
      const { data, error } = await supabase
        .schema("public")
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error && error.code === "PGRST116") {
        // Not found
        // Optionally, create a profile here for debugging
        // await supabase.from("profiles").insert({ id: userId, ... });
        return null;
      }
      return data as UserProfile;
    },
    [supabase]
  );

  // Handle auth state changes
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(
      async (event: string, session: Session | null) => {
        if (event === "SIGNED_IN" && session?.user) {
          setUser(session.user);
          const userProfile = await fetchProfile(session.user.id);
          // If no profile exists, it's expected to be created by another process or already exist.
          // We will just attempt to set it if fetched.
          if (userProfile) {
            setProfile(userProfile);
          } else {
            // It's possible the profile is not yet available, or there's an issue.
            // DashboardLayout will handle retrying or showing an error.
            console.warn(
              "Profile not found on SIGNED_IN for user:",
              session.user.id
            );
          }
          router.push(redirectTo);
        } else if (event === "SIGNED_OUT") {
          setUser(null);
          setProfile(null);
        } else if (event === "USER_UPDATED" && session?.user) {
          setUser(session.user);
          const userProfile = await fetchProfile(session.user.id);
          if (userProfile) {
            setProfile(userProfile);
          }
        }
      }
    );

    const getSession = async () => {
      try {
        const {
          data: { session: currentSession },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) {
          console.error("Error fetching session:", sessionError);
          toast({
            title: "Session Error",
            description: "Could not retrieve your current session.",
            variant: "destructive",
          });
          setIsLoading(false);
          return;
        }

        if (currentSession?.user) {
          setUser(currentSession.user);
          const userProfile = await fetchProfile(currentSession.user.id);
          // If no profile exists, it's expected to be created by another process or already exist.
          if (userProfile) {
            setProfile(userProfile);
          } else {
            console.warn(
              "Profile not found in getSession for user:",
              currentSession.user.id
            );
            // DashboardLayout will handle retrying or showing an error.
          }
        }
      } catch (error) {
        console.error("Error getting session:", error);
        toast({
          title: "Authentication Error",
          description:
            "Failed to retrieve your session. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    getSession();

    return () => {
      subscription?.unsubscribe();
    };
  }, [fetchProfile, redirectTo, router, supabase.auth]);

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      if (data?.user) {
        setUser(data.user);
        const userProfile = await fetchProfile(data.user.id);
        // If no profile exists, it's expected to be created by another process or already exist.
        if (userProfile) {
          setProfile(userProfile);
        } else {
          console.warn(
            "Profile not found after email/password sign-in for user:",
            data.user.id
          );
        }
      }
      return { error: null };
    } catch (error: any) {
      console.error("Sign in error:", error);
      toast({
        title: "Error",
        description: error.message || "An error occurred during sign in",
        variant: "destructive",
      });
      return { error };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const signInWithProvider = async (provider: "google" | "github") => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${
            window.location.origin
          }/auth/callback?next=${encodeURIComponent(redirectTo)}`,
        },
      });

      if (error) {
        toast({
          title: "OAuth Error",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
    } catch (error: any) {
      console.error("Provider sign in error:", error);
      toast({
        title: "Error",
        description: error.message || "An error occurred during sign in",
        variant: "destructive",
      });
    }
  };

  const signUp = async (
    email: string,
    password: string,
    fullName: string,
    organizationName: string, // Added organizationName
    captchaToken: string
  ) => {
    setIsLoading(true);
    try {
      // 1. Check if organization already exists by name (case-insensitive for robustness)
      const orgNameLower = organizationName.toLowerCase();
      const { data: existingOrg, error: existingOrgError } = await supabase
        .from("organizations")
        .select("id, name, slug")
        .ilike("name", orgNameLower) // Case-insensitive search
        .maybeSingle(); // Use maybeSingle to not error if no org is found

      if (existingOrgError && existingOrgError.code !== "PGRST116") {
        // PGRST116 means no rows found, which is fine
        console.error(
          "Error checking for existing organization:",
          existingOrgError
        );
        toast({
          variant: "destructive",
          title: "Organization Check Failed",
          description:
            existingOrgError.message || "Could not verify organization name.",
        });
        return { error: existingOrgError };
      }

      if (existingOrg) {
        toast({
          variant: "destructive",
          title: "Organization Already Exists",
          description: `An organization named "${existingOrg.name}" already exists. Please choose a different name or contact support if you believe this is an error.`,
        });
        return { error: { message: "Organization already exists" } }; // Specific error for UI to potentially handle
      }

      // 2. Sign up the user
      const { data: signUpData, error: signUpError } =
        await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName,
            },
            captchaToken,
          },
        });

      if (signUpError) {
        console.error("Supabase sign up error:", signUpError);
        toast({
          variant: "destructive",
          title: "Sign Up Failed",
          description: signUpError.message || "Could not create your account.",
        });
        return { error: signUpError };
      }

      if (!signUpData.user) {
        toast({
          variant: "destructive",
          title: "Sign Up Failed",
          description:
            "User creation failed unexpectedly after CAPTCHA validation.",
        });
        return { error: { message: "User creation failed post-captcha." } };
      }

      const user = signUpData.user;

      // 3. Create the organization
      const slug = organizationName
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9-]/g, "");

      const { data: newOrg, error: newOrgError } = await supabase
        .from("organizations")
        .insert({
          name: organizationName,
          slug: slug,
        })
        .select()
        .single();

      if (newOrgError) {
        console.error("Error creating organization:", newOrgError);
        toast({
          variant: "destructive",
          title: "Organization Creation Failed",
          description:
            newOrgError.message ||
            "Could not create your organization after user sign-up.",
        });
        return { error: newOrgError };
      }

      // 4. Add user as owner to the organization_members table
      const { error: memberError } = await supabase
        .from("organization_members")
        .insert({
          organization_id: newOrg.id,
          user_id: user.id,
          role: "owner",
        });

      if (memberError) {
        console.error("Error adding user to organization:", memberError);
        toast({
          variant: "destructive",
          title: "Failed to Add to Organization",
          description:
            memberError.message ||
            "Could not assign your role in the new organization.",
        });
        return { error: memberError };
      }

      if (user && !user.email_confirmed_at) {
        toast({
          title: "Account & Organization Created",
          description:
            "Check your email for the confirmation link. Your organization is ready!",
        });
      } else if (user && user.email_confirmed_at) {
        toast({
          title: "Account & Organization Ready",
          description: "Your account and organization are set up!",
        });
      }

      return { error: null };
    } catch (error: any) {
      console.error("General sign up / organization creation error:", error);
      toast({
        variant: "destructive",
        title: "Sign Up Error",
        description:
          error.message ||
          "An unexpected error occurred during the sign-up process.",
      });
      return { error };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;
      return { error: null };
    } catch (error: any) {
      console.error("Reset password error:", error);
      return { error };
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { error: new Error("No user logged in") };

    try {
      const { error } = await supabase
        .from("profiles")
        .update(updates)
        .eq("id", user.id);

      if (error) throw error;

      const updatedProfile = await fetchProfile(user.id);
      if (updatedProfile) {
        setProfile(updatedProfile);
      }

      return { error: null };
    } catch (error: any) {
      console.error("Update profile error:", error);
      return { error };
    }
  };

  const updatePassword = async (password: string) => {
    try {
      console.log("Updating password...");
      const { error } = await supabase.auth.updateUser({
        password,
      });

      if (error) throw error;
      return { error: null };
    } catch (error: any) {
      console.error("Update password error:", error);
      return { error };
    }
  };

  const hasRole = (roles: UserRole[]) => {
    if (!profile) return false;
    return roles.includes(profile.role);
  };

  const refreshProfile = async () => {
    console.log("Refreshing profile...");
    console.log("user is ", user);
    if (!user) return;
    const updatedProfile = await fetchProfile(user.id);
    if (updatedProfile) {
      setProfile(updatedProfile);
    }
  };

  const value = {
    user,
    profile,
    isLoading,
    signIn,
    signUp,
    signOut,
    signInWithProvider,
    resetPassword,
    updatePassword,
    updateProfile,
    hasRole,
    refreshProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
