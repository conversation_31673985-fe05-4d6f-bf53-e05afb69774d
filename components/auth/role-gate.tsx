"use client"

import type React from "react"

import { useRole } from "./use-role"
import { UserRole } from "@/components/auth/auth-provider"

interface RoleGateProps {
  children: React.ReactNode
  allowedRoles: UserRole[]
  fallback?: React.ReactNode
}

export function RoleGate({ children, allowedRoles, fallback }: RoleGateProps) {
  const { hasRole } = useRole()

  if (hasRole(allowedRoles)) {
    return <>{children}</>
  }

  return fallback ? <>{fallback}</> : null
}
