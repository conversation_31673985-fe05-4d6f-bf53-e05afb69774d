"use client"

import { useAuth, UserRole } from "./auth-provider"

// Hook to check user's role
export function useRole() {
  const { profile, hasRole } = useAuth()

  // Function to check if user has one of the specified roles
  const hasOneOfRoles = (roles: UserRole[]) => {
    return hasRole(roles)
  }

  // Convenience functions for common role checks
  const isPro = () => hasRole(["pro", "enterprise"])
  const isEnterprise = () => hasRole(["enterprise"])
  const isFree = () => profile?.role === "free"

  // Get the current user's role
  const currentRole = profile?.role || null

  return {
    hasRole: hasOneOfRoles,
    isPro,
    isEnterprise,
    isFree,
    currentRole,
  }
}
