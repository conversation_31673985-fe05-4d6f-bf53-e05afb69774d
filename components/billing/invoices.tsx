"use client"

import { useState } from "react"
import { motion, useReducedMotion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Table,
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious
} from "@/components/ui/pagination"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Download, 
  Filter, 
  FileText, 
  Search, 
  Calendar, 
  CreditCard, 
  CheckCircle, 
  Clock,
  ExternalLink,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { format, addMonths } from "date-fns"

// Generate mock invoices data
const generateMockInvoices = () => {
  const invoices = []
  const currentDate = new Date()
  
  // Past 12 months of invoices
  for (let i = 0; i < 12; i++) {
    const date = addMonths(currentDate, -i)
    const isPaid = i < 11
    
    invoices.push({
      id: `INV-${2025 - Math.floor(i/12)}-${String(12 - (i % 12)).padStart(2, "0")}${Math.floor(Math.random() * 1000).toString().padStart(3, "0")}`,
      date: format(date, "yyyy-MM-dd"),
      amount: 49,
      status: isPaid ? "paid" : "pending",
      paymentMethod: isPaid ? "Visa •••• 4242" : null
    })
  }
  
  return invoices
}

const mockInvoices = generateMockInvoices()

export function BillingInvoices() {
  const [invoices, setInvoices] = useState(mockInvoices)
  const [selectedInvoice, setSelectedInvoice] = useState<typeof mockInvoices[0] | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [viewingInvoice, setViewingInvoice] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  
  const prefersReducedMotion = useReducedMotion()
  const itemsPerPage = 6
  
  // Filter and paginate invoices
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.id.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === "all" || invoice.status === filterStatus
    return matchesSearch && matchesStatus
  })
  
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage)
  const paginatedInvoices = filteredInvoices.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )
  
  // Status renderers
  const StatusBadge = ({ status }: { status: string }) => {
    if (status === "paid") {
      return (
        <Badge className="bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-400">
          <CheckCircle className="mr-1 h-3 w-3" /> Paid
        </Badge>
      )
    }
    
    if (status === "pending") {
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 dark:bg-amber-950 dark:text-amber-400">
          <Clock className="mr-1 h-3 w-3" /> Pending
        </Badge>
      )
    }
    
    return <Badge variant="outline">{status}</Badge>
  }
  
  // Download handler
  const handleDownload = (invoice: typeof mockInvoices[0]) => {
    setIsDownloading(true)
    
    // Simulate download
    setTimeout(() => {
      setIsDownloading(false)
      // In a real app, this would trigger the download of a PDF or redirect to a download URL
    }, 1500)
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div>
          <h3 className="text-xl font-semibold">Invoice History</h3>
          <p className="text-sm text-muted-foreground">
            View and download your billing history
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="relative w-full sm:w-[180px]">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="Search invoices" 
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value)
                setCurrentPage(1) // Reset to first page when searching
              }}
              className="pl-8"
            />
          </div>
          
          <Select 
            value={filterStatus} 
            onValueChange={(value) => {
              setFilterStatus(value)
              setCurrentPage(1) // Reset to first page when filtering
            }}
          >
            <SelectTrigger className="sm:w-[140px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>Status</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Invoices</CardTitle>
          <CardDescription>
            {filteredInvoices.length} {filteredInvoices.length === 1 ? "invoice" : "invoices"} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <motion.div
            initial={prefersReducedMotion ? { opacity: 1 } : "hidden"}
            animate={prefersReducedMotion ? { opacity: 1 } : "visible"}
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05
                }
              }
            }}
          >
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedInvoices.length > 0 ? (
                  paginatedInvoices.map((invoice) => (
                    <motion.tr key={invoice.id} variants={itemVariants} className="group">
                      <TableCell className="font-medium">{invoice.id}</TableCell>
                      <TableCell>{format(new Date(invoice.date), "MMM dd, yyyy")}</TableCell>
                      <TableCell>${invoice.amount.toFixed(2)}</TableCell>
                      <TableCell>
                        <StatusBadge status={invoice.status} />
                      </TableCell>
                      <TableCell>
                        {invoice.paymentMethod ? (
                          <div className="flex items-center text-sm">
                            <CreditCard className="mr-1 h-3.5 w-3.5 text-muted-foreground" />
                            {invoice.paymentMethod}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Dialog open={viewingInvoice && selectedInvoice?.id === invoice.id} onOpenChange={(open) => !open && setViewingInvoice(false)}>
                            <DialogTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                className="invisible group-hover:visible"
                                onClick={() => {
                                  setSelectedInvoice(invoice)
                                  setViewingInvoice(true)
                                }}
                              >
                                <FileText className="mr-1 h-4 w-4" /> View
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-3xl">
                              <DialogHeader>
                                <DialogTitle className="flex items-center justify-between">
                                  <span>Invoice {selectedInvoice?.id}</span>
                                  <StatusBadge status={selectedInvoice?.status || ""} />
                                </DialogTitle>
                                <DialogDescription>
                                  Issued on {selectedInvoice && format(new Date(selectedInvoice.date), "MMMM dd, yyyy")}
                                </DialogDescription>
                              </DialogHeader>
                              
                              <ScrollArea className="h-[50vh] rounded-md border p-4">
                                <div className="space-y-8">
                                  {/* Mock invoice content */}
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="text-xl font-bold text-primary">Commuza</div>
                                      <div className="text-sm text-muted-foreground">AI-powered community managers</div>
                                    </div>
                                    <div className="text-right">
                                      <div className="text-sm font-medium">Invoice #{selectedInvoice?.id}</div>
                                      <div className="text-xs text-muted-foreground">
                                        Issued: {selectedInvoice && format(new Date(selectedInvoice.date), "MMM dd, yyyy")}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="grid grid-cols-2 gap-8">
                                    <div>
                                      <div className="text-sm font-medium">Billed To</div>
                                      <div className="mt-2 text-sm">
                                        <div>John Smith</div>
                                        <div>Example Company Inc.</div>
                                        <div><EMAIL></div>
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium">Payment Details</div>
                                      <div className="mt-2 grid grid-cols-2 gap-1 text-sm">
                                        <div className="text-muted-foreground">Amount:</div>
                                        <div>${selectedInvoice?.amount.toFixed(2)} USD</div>
                                        <div className="text-muted-foreground">Status:</div>
                                        <div className="capitalize">{selectedInvoice?.status}</div>
                                        <div className="text-muted-foreground">Method:</div>
                                        <div>{selectedInvoice?.paymentMethod || "Not paid yet"}</div>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <div className="text-sm font-medium mb-4">Invoice Items</div>
                                    <Table>
                                      <TableHeader>
                                        <TableRow>
                                          <TableHead>Description</TableHead>
                                          <TableHead className="text-right">Amount</TableHead>
                                        </TableRow>
                                      </TableHeader>
                                      <TableBody>
                                        <TableRow>
                                          <TableCell>
                                            <div>
                                              <div className="font-medium">Pro Plan</div>
                                              <div className="text-xs text-muted-foreground">
                                                Monthly subscription
                                              </div>
                                            </div>
                                          </TableCell>
                                          <TableCell className="text-right">${selectedInvoice?.amount.toFixed(2)}</TableCell>
                                        </TableRow>
                                      </TableBody>
                                    </Table>
                                  </div>
                                  
                                  <div className="border-t border-border pt-4">
                                    <div className="flex items-center justify-between font-medium">
                                      <div>Total</div>
                                      <div>${selectedInvoice?.amount.toFixed(2)} USD</div>
                                    </div>
                                  </div>
                                  
                                  <div className="border-t border-border pt-4 text-center text-xs text-muted-foreground">
                                    <p>Thank you for your business!</p>
                                    <p>If you have any questions, contact <NAME_EMAIL></p>
                                  </div>
                                </div>
                              </ScrollArea>
                              
                              <DialogFooter>
                                <Button 
                                  onClick={() => handleDownload(invoice)}
                                  disabled={isDownloading}
                                >
                                  <Download className="mr-2 h-4 w-4" />
                                  {isDownloading ? "Downloading..." : "Download PDF"}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                          
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDownload(invoice)}
                            disabled={isDownloading && selectedInvoice?.id === invoice.id}
                            className={invoice.status !== "paid" ? "invisible group-hover:visible" : ""}
                          >
                            <Download className="mr-1 h-4 w-4" /> 
                            {isDownloading && selectedInvoice?.id === invoice.id ? "Downloading..." : "Download"}
                          </Button>
                        </div>
                      </TableCell>
                    </motion.tr>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center space-y-2">
                        <FileText className="h-12 w-12 text-muted-foreground/40" />
                        <div className="text-lg font-medium">No invoices found</div>
                        <div className="text-sm text-muted-foreground">
                          Try adjusting your search or filter to find what you're looking for.
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </motion.div>
        </CardContent>
        <CardFooter className="flex items-center justify-between border-t px-6 py-4">
          <div className="text-xs text-muted-foreground">
            Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredInvoices.length)} to {Math.min(currentPage * itemsPerPage, filteredInvoices.length)} of {filteredInvoices.length} invoices
          </div>
          
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  href="#" 
                  onClick={(e) => {
                    e.preventDefault()
                    if (currentPage > 1) setCurrentPage(currentPage - 1)
                  }} 
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
              
              {Array.from({ length: totalPages }).map((_, i) => {
                const page = i + 1
                // Show current page, first and last page, and one page before and after current
                if (
                  page === 1 || 
                  page === totalPages || 
                  page === currentPage || 
                  page === currentPage - 1 || 
                  page === currentPage + 1
                ) {
                  return (
                    <PaginationItem key={page}>
                      <PaginationLink 
                        href="#" 
                        isActive={page === currentPage}
                        onClick={(e) => {
                          e.preventDefault()
                          setCurrentPage(page)
                        }}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  )
                }
                
                // Show ellipsis for gaps
                if (page === currentPage - 2 || page === currentPage + 2) {
                  return <PaginationItem key={page}>...</PaginationItem>
                }
                
                return null
              })}
              
              <PaginationItem>
                <PaginationNext 
                  href="#" 
                  onClick={(e) => {
                    e.preventDefault()
                    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
                  }}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardFooter>
      </Card>
      
      <div className="rounded-lg border p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex items-center">
            <div className="mr-4 h-10 w-10 flex items-center justify-center rounded-full bg-primary/10">
              <FileText className="h-5 w-5 text-primary" />
            </div>
            <div>
              <div className="font-medium">Need additional invoice information?</div>
              <div className="text-sm text-muted-foreground">
                Contact our billing team for detailed receipts or custom invoices.
              </div>
            </div>
          </div>
          <Button variant="outline" className="shrink-0">
            <ExternalLink className="mr-2 h-4 w-4" /> Contact Billing Support
          </Button>
        </div>
      </div>
    </div>
  )
}