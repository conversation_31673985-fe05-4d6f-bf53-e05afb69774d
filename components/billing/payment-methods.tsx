"use client"

import { useState } from "react"
import { motion, useReducedMotion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Dialog,
  DialogContent, 
  DialogDescription, 
  DialogFooter,
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Separator } from "@/components/ui/separator"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { 
  CreditCard, 
  Pencil, 
  MoreHorizontal, 
  Plus, 
  StarIcon, 
  Trash2,
  Shield,
  CheckCircle
} from "lucide-react"

// Mock payment methods
const mockPaymentMethods = [
  {
    id: "pm_1LmnOP2eZvKYlo2CghTv7",
    type: "card",
    brand: "visa",
    last4: "4242",
    expMonth: 4,
    expYear: 2026,
    default: true,
    billingAddress: {
      line1: "123 Main St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "US"
    }
  },
  {
    id: "pm_1KjnOP3eZvKYlo2Dab8Hj",
    type: "card",
    brand: "mastercard",
    last4: "5555",
    expMonth: 9,
    expYear: 2025,
    default: false,
    billingAddress: {
      line1: "456 Market St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "US"
    }
  }
]

type PaymentMethod = typeof mockPaymentMethods[0]

export function BillingPaymentMethods() {
  const [paymentMethods, setPaymentMethods] = useState(mockPaymentMethods)
  const [isAddingCard, setIsAddingCard] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null)
  const [isEditingAddress, setIsEditingAddress] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  
  const prefersReducedMotion = useReducedMotion()

  // Get brand icon or return default credit card icon
  const getCardIcon = (brand: string) => {
    return <CreditCard className="h-4 w-4" />
  }
  
  // Get formatted expiration date
  const getExpiration = (month: number, year: number) => {
    return `${month.toString().padStart(2, '0')}/${year.toString().slice(-2)}`
  }
  
  // Handle setting a payment method as default
  const handleSetDefault = (id: string) => {
    setIsProcessing(true)
    
    // Simulate API call
    setTimeout(() => {
      setPaymentMethods(paymentMethods.map(pm => ({
        ...pm,
        default: pm.id === id
      })))
      
      toast({
        title: "Default payment method updated",
        description: "Your default payment method has been updated successfully."
      })
      
      setIsProcessing(false)
    }, 1000)
  }
  
  // Handle deletion of a payment method
  const handleDeletePaymentMethod = () => {
    if (!selectedPaymentMethod) return
    
    setIsProcessing(true)
    
    // Simulate API call
    setTimeout(() => {
      setPaymentMethods(paymentMethods.filter(pm => pm.id !== selectedPaymentMethod.id))
      
      toast({
        title: "Payment method removed",
        description: `Your ${selectedPaymentMethod.brand} card ending in ${selectedPaymentMethod.last4} has been removed.`
      })
      
      setIsDeleting(false)
      setSelectedPaymentMethod(null)
      setIsProcessing(false)
    }, 1000)
  }
  
  // Handle adding a new payment method
  const handleAddPaymentMethod = (e: React.FormEvent) => {
    e.preventDefault()
    setIsProcessing(true)
    
    // Simulate API call
    setTimeout(() => {
      const newCard = {
        id: `pm_${Math.random().toString(36).substr(2, 9)}`,
        type: "card",
        brand: "amex",
        last4: "1234",
        expMonth: 12,
        expYear: 2028,
        default: false,
        billingAddress: {
          line1: "789 New St",
          city: "San Francisco",
          state: "CA",
          postalCode: "94105",
          country: "US"
        }
      }
      
      setPaymentMethods([...paymentMethods, newCard])
      
      toast({
        title: "Payment method added",
        description: "Your new card has been added successfully."
      })
      
      setIsAddingCard(false)
      setIsProcessing(false)
    }, 1500)
  }
  
  // Handle updating billing address
  const handleUpdateAddress = (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedPaymentMethod) return
    
    setIsProcessing(true)
    
    // Simulate API call
    setTimeout(() => {
      setPaymentMethods(paymentMethods.map(pm => 
        pm.id === selectedPaymentMethod.id 
          ? { 
              ...pm, 
              billingAddress: { 
                ...pm.billingAddress,
                line1: "Updated Address Line"
              } 
            } 
          : pm
      ))
      
      toast({
        title: "Billing address updated",
        description: "Your billing address has been updated successfully."
      })
      
      setIsEditingAddress(false)
      setSelectedPaymentMethod(null)
      setIsProcessing(false)
    }, 1000)
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold">Payment Methods</h3>
        <p className="text-sm text-muted-foreground">
          Manage your payment methods and billing addresses
        </p>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-0.5">
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>Your saved payment methods</CardDescription>
          </div>
          <Dialog open={isAddingCard} onOpenChange={setIsAddingCard}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add Payment Method</DialogTitle>
                <DialogDescription>
                  Add a new credit or debit card to your account
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddPaymentMethod}>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="card-name">Cardholder Name</Label>
                    <Input id="card-name" placeholder="Name as it appears on card" />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="card-number">Card Number</Label>
                    <Input id="card-number" placeholder="•••• •••• •••• ••••" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="expiry-date">Expiry Date</Label>
                      <Input id="expiry-date" placeholder="MM/YY" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="cvc">CVC</Label>
                      <Input id="cvc" placeholder="•••" />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="address">Address Line</Label>
                    <Input id="address" placeholder="Street address" />
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="city">City</Label>
                      <Input id="city" placeholder="City" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="state">State</Label>
                      <Input id="state" placeholder="State" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="postalCode">Postal Code</Label>
                      <Input id="postalCode" placeholder="Postal code" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox id="make-default" />
                    <Label htmlFor="make-default" className="text-sm font-normal">
                      Make this my default payment method
                    </Label>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Shield className="h-4 w-4 mr-1" /> Secure payment processing
                  </div>
                  <DialogFooter>
                    <Button 
                      type="submit" 
                      disabled={isProcessing}
                    >
                      {isProcessing ? "Adding..." : "Add Card"}
                    </Button>
                  </DialogFooter>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {paymentMethods.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <CreditCard className="h-12 w-12 text-muted-foreground/50 mb-3" />
              <h3 className="text-lg font-medium">No payment methods</h3>
              <p className="text-sm text-muted-foreground mb-4">
                You haven't added any payment methods yet.
              </p>
              <Button onClick={() => setIsAddingCard(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
            </div>
          ) : (
            <motion.div 
              className="space-y-2"
              initial={prefersReducedMotion ? { opacity: 1 } : "hidden"}
              animate={prefersReducedMotion ? { opacity: 1 } : "visible"}
              variants={containerVariants}
            >
              {paymentMethods.map((paymentMethod) => (
                <motion.div key={paymentMethod.id} variants={itemVariants}>
                  <div className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                        {getCardIcon(paymentMethod.brand)}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <p className="font-medium capitalize">
                            {paymentMethod.brand} •••• {paymentMethod.last4}
                          </p>
                          {paymentMethod.default && (
                            <Badge variant="outline" className="ml-2 border-primary text-primary">
                              Default
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Expires {getExpiration(paymentMethod.expMonth, paymentMethod.expYear)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {!paymentMethod.default && (
                            <DropdownMenuItem 
                              onClick={() => handleSetDefault(paymentMethod.id)}
                              disabled={isProcessing}
                            >
                              <StarIcon className="mr-2 h-4 w-4" />
                              <span>Set as default</span>
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedPaymentMethod(paymentMethod)
                              setIsEditingAddress(true)
                            }}
                            disabled={isProcessing}
                          >
                            <Pencil className="mr-2 h-4 w-4" />
                            <span>Edit billing address</span>
                          </DropdownMenuItem>
                          {!paymentMethod.default && (
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={() => {
                                setSelectedPaymentMethod(paymentMethod)
                                setIsDeleting(true)
                              }}
                              disabled={isProcessing}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>Remove</span>
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Delete Payment Method Dialog */}
      <AlertDialog open={isDeleting} onOpenChange={setIsDeleting}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Payment Method</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this payment method? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault()
                handleDeletePaymentMethod()
              }}
              className="bg-destructive hover:bg-destructive/90"
              disabled={isProcessing}
            >
              {isProcessing ? "Removing..." : "Remove"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Billing Address Dialog */}
      <Dialog open={isEditingAddress} onOpenChange={setIsEditingAddress}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Billing Address</DialogTitle>
            <DialogDescription>
              Update the billing address for your {selectedPaymentMethod?.brand} card ending in {selectedPaymentMethod?.last4}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateAddress}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-address">Address Line</Label>
                <Input 
                  id="edit-address" 
                  defaultValue={selectedPaymentMethod?.billingAddress.line1} 
                  placeholder="Street address" 
                />
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-city">City</Label>
                  <Input 
                    id="edit-city" 
                    defaultValue={selectedPaymentMethod?.billingAddress.city} 
                    placeholder="City" 
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-state">State</Label>
                  <Input 
                    id="edit-state" 
                    defaultValue={selectedPaymentMethod?.billingAddress.state} 
                    placeholder="State" 
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-postalCode">Postal Code</Label>
                  <Input 
                    id="edit-postalCode" 
                    defaultValue={selectedPaymentMethod?.billingAddress.postalCode} 
                    placeholder="Postal code" 
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-country">Country</Label>
                <Input 
                  id="edit-country" 
                  defaultValue={selectedPaymentMethod?.billingAddress.country} 
                  placeholder="Country" 
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                disabled={isProcessing}
              >
                {isProcessing ? "Updating..." : "Update Address"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      <div className="rounded-md bg-muted p-4">
        <div className="flex">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <Shield className="h-5 w-5 text-primary" />
          </div>
          <div className="ml-4">
            <h5 className="text-sm font-medium">Secure payment processing</h5>
            <p className="text-sm text-muted-foreground">
              All payment information is stored securely by our payment processor. 
              Commuza does not store your full card details.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}