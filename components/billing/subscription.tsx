"use client"

import { useState } from "react"
import { motion, useReducedMotion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Dialog,
  DialogContent, 
  DialogDescription, 
  DialogFooter,
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Check, AlertCircle, CreditCard, Zap, Calendar, ArrowRight, X } from "lucide-react"
import { format, addMonths } from "date-fns"

// Mock subscription data
const mockSubscription = {
  plan: "pro",
  status: "active",
  billingCycle: "monthly",
  currentPeriodStart: new Date(),
  currentPeriodEnd: addMonths(new Date(), 1),
  price: 49,
  paymentMethod: {
    id: "pm_1LmnOP2eZvKYlo2CghTv7",
    type: "card",
    brand: "visa",
    last4: "4242",
    expMonth: 4,
    expYear: 2026
  },
  features: [
    "3 AI Community Managers",
    "3 platform integrations",
    "Advanced moderation", 
    "Custom personalities",
    "1,000 AI interactions/day",
    "30-day data retention",
    "Analytics dashboard",
    "Priority support"
  ],
  autoRenew: true
}

// Available plans for upgrading/downgrading
const availablePlans = [
  {
    name: "Free",
    description: "For small communities just getting started.",
    price: 0,
    cycle: "month",
    features: [
      "1 AI Community Manager",
      "1 platform integration",
      "Basic moderation",
      "Community engagement",
      "100 AI interactions/day",
      "7-day data retention"
    ],
    popular: false,
    recommended: false
  },
  {
    name: "Pro",
    description: "For growing communities that need more capabilities.",
    price: 49,
    cycle: "month",
    features: [
      "3 AI Community Managers",
      "3 platform integrations",
      "Advanced moderation",
      "Custom personalities",
      "1,000 AI interactions/day",
      "30-day data retention",
      "Analytics dashboard",
      "Priority support"
    ],
    popular: true,
    recommended: false
  },
  {
    name: "Enterprise",
    description: "For large communities with custom requirements.",
    price: "Custom",
    cycle: "",
    features: [
      "Unlimited AI Community Managers",
      "All platform integrations",
      "Custom AI training",
      "Advanced analytics",
      "Unlimited AI interactions",
      "90-day data retention",
      "Dedicated support",
      "Custom integrations",
      "SLA guarantees"
    ],
    popular: false,
    recommended: mockSubscription.plan === "pro"
  }
]

export function BillingSubscription() {
  const [subscription, setSubscription] = useState(mockSubscription)
  const [isChangingPlan, setIsChangingPlan] = useState(false)
  const [isChangingBillingCycle, setIsChangingBillingCycle] = useState(false)
  const [isCancelling, setIsCancelling] = useState(false)
  const [isProcessingAction, setIsProcessingAction] = useState(false)
  const [processingActionType, setProcessingActionType] = useState<string | null>(null)
  
  const prefersReducedMotion = useReducedMotion()
  
  // Find current plan details
  const currentPlan = availablePlans.find(plan => plan.name.toLowerCase() === subscription.plan) || availablePlans[0]
  
  // Days remaining in current billing period
  const today = new Date()
  const endDate = subscription.currentPeriodEnd
  const totalDays = Math.round((endDate.getTime() - subscription.currentPeriodStart.getTime()) / (1000 * 60 * 60 * 24))
  const daysRemaining = Math.max(0, Math.round((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)))
  const percentRemaining = (daysRemaining / totalDays) * 100
  
  // Handle plan change
  const handlePlanChange = (planName: string) => {
    setIsProcessingAction(true)
    setProcessingActionType("plan")
    
    // Simulate plan change API call
    setTimeout(() => {
      setSubscription({
        ...subscription,
        plan: planName.toLowerCase()
      })
      setIsChangingPlan(false)
      setIsProcessingAction(false)
      setProcessingActionType(null)
    }, 1500)
  }
  
  // Handle billing cycle change
  const handleBillingCycleChange = (cycle: "monthly" | "yearly") => {
    setIsProcessingAction(true)
    setProcessingActionType("billing")
    
    // Simulate billing cycle change API call
    setTimeout(() => {
      setSubscription({
        ...subscription,
        billingCycle: cycle,
        price: cycle === "yearly" ? 490 : 49,
        currentPeriodEnd: cycle === "yearly" ? addMonths(today, 12) : addMonths(today, 1)
      })
      setIsChangingBillingCycle(false)
      setIsProcessingAction(false)
      setProcessingActionType(null)
    }, 1500)
  }
  
  // Handle cancellation
  const handleCancellation = () => {
    setIsProcessingAction(true)
    setProcessingActionType("cancel")
    
    // Simulate cancellation API call
    setTimeout(() => {
      setSubscription({
        ...subscription,
        autoRenew: false,
        status: "canceled"
      })
      setIsCancelling(false)
      setIsProcessingAction(false)
      setProcessingActionType(null)
    }, 1500)
  }
  
  // Handle reactivation
  const handleReactivate = () => {
    setIsProcessingAction(true)
    setProcessingActionType("reactivate")
    
    // Simulate reactivation API call
    setTimeout(() => {
      setSubscription({
        ...subscription,
        autoRenew: true,
        status: "active"
      })
      setIsProcessingAction(false)
      setProcessingActionType(null)
    }, 1500)
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold">Subscription</h3>
        <p className="text-sm text-muted-foreground">
          Manage your subscription plan and billing preferences
        </p>
      </div>

      {/* Current Plan */}
      <Card>
        <CardHeader className="space-y-1 pb-4">
          <div className="flex items-center justify-between">
            <CardTitle>Current Plan</CardTitle>
            <Badge variant={subscription.status === "active" ? "default" : "secondary"}>
              {subscription.status === "active" ? "Active" : "Canceled"}
            </Badge>
          </div>
          <CardDescription>
            Your subscription renews on {format(subscription.currentPeriodEnd, "MMMM dd, yyyy")}
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-6">
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div className="space-y-1">
                <div className="flex items-center">
                  <h3 className="text-2xl font-bold capitalize">{subscription.plan}</h3>
                  {currentPlan.popular && (
                    <Badge className="ml-2 bg-primary/10 text-primary">Popular</Badge>
                  )}
                </div>
                <div className="flex items-baseline space-x-1">
                  <span className="text-lg font-semibold">
                    ${typeof currentPlan.price === "number" ? currentPlan.price : "Custom"}
                  </span>
                  {typeof currentPlan.price === "number" && (
                    <span className="text-muted-foreground">/{currentPlan.cycle}</span>
                  )}
                </div>
              </div>
              
              <Dialog open={isChangingPlan} onOpenChange={setIsChangingPlan}>
                <DialogTrigger asChild>
                  <Button variant="outline">Change Plan</Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                  <DialogHeader>
                    <DialogTitle>Change Subscription Plan</DialogTitle>
                    <DialogDescription>
                      Choose the plan that best suits your needs. Changes will take effect immediately.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="grid gap-6 py-4 md:grid-cols-3">
                    {availablePlans.map((plan) => (
                      <Card key={plan.name} className={`relative overflow-hidden ${plan.name.toLowerCase() === subscription.plan ? "border-primary ring-1 ring-primary" : ""}`}>
                        {plan.name.toLowerCase() === subscription.plan && (
                          <div className="absolute right-0 top-0 h-16 w-16">
                            <div className="absolute right-0 top-0 h-8 w-24 -translate-y-4 rotate-45 bg-primary text-center text-xs font-semibold leading-8 text-white">
                              Current
                            </div>
                          </div>
                        )}
                        
                        {plan.popular && (
                          <div className="absolute left-0 right-0 top-0 flex justify-center">
                            <div className="bg-primary text-primary-foreground text-xs rounded-b-lg font-semibold px-3 py-1">
                              Most Popular
                            </div>
                          </div>
                        )}
                        
                        {plan.recommended && (
                          <div className="absolute left-0 right-0 top-0 flex justify-center">
                            <div className="bg-emerald-600 text-white text-xs rounded-b-lg font-semibold px-3 py-1">
                              Recommended
                            </div>
                          </div>
                        )}
                        
                        <CardHeader className={`${(plan.popular || plan.recommended) ? "pt-8" : "pt-6"} pb-2`}>
                          <CardTitle className="text-lg">{plan.name}</CardTitle>
                          <CardDescription className="min-h-[40px]">{plan.description}</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="mb-4">
                            <div className="flex items-baseline">
                              <span className="text-3xl font-bold">
                                {typeof plan.price === "number" ? `$${plan.price}` : plan.price}
                              </span>
                              {plan.cycle && (
                                <span className="ml-1 text-sm text-muted-foreground">/{plan.cycle}</span>
                              )}
                            </div>
                          </div>
                          
                          <ul className="space-y-2 text-sm">
                            {plan.features.map((feature) => (
                              <li key={feature} className="flex items-start">
                                <Check className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                                <span>{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                        <CardFooter>
                          {plan.name.toLowerCase() === subscription.plan ? (
                            <Button className="w-full" disabled>Current Plan</Button>
                          ) : (
                            <Button
                              variant={plan.popular || plan.recommended ? "default" : "outline"}
                              className="w-full"
                              onClick={() => handlePlanChange(plan.name)}
                              disabled={isProcessingAction && processingActionType === "plan"}
                            >
                              {isProcessingAction && processingActionType === "plan"
                                ? "Switching..."
                                : `Switch to ${plan.name}`
                              }
                            </Button>
                          )}
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="mb-3 font-medium">Included in your plan:</h4>
              <motion.ul 
                className="grid gap-2 md:grid-cols-2"
                initial={prefersReducedMotion ? { opacity: 1 } : "hidden"}
                animate={prefersReducedMotion ? { opacity: 1 } : "visible"}
                variants={containerVariants}
              >
                {subscription.features.map((feature) => (
                  <motion.li key={feature} className="flex items-start" variants={itemVariants}>
                    <Check className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                    <span className="text-sm">{feature}</span>
                  </motion.li>
                ))}
              </motion.ul>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Current billing period</span>
                </div>
                <span className="text-sm">
                  {format(subscription.currentPeriodStart, "MMM dd")} - {format(subscription.currentPeriodEnd, "MMM dd, yyyy")}
                </span>
              </div>
              
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{daysRemaining} days remaining</span>
                  <span>{Math.round(percentRemaining)}%</span>
                </div>
                <Progress value={percentRemaining} className="h-2" />
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center text-sm">
                <CreditCard className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>
                  Billing with {subscription.paymentMethod.brand.charAt(0).toUpperCase() + subscription.paymentMethod.brand.slice(1)} •••• {subscription.paymentMethod.last4}
                </span>
              </div>
              
              {subscription.billingCycle === "monthly" && (
                <Dialog open={isChangingBillingCycle} onOpenChange={setIsChangingBillingCycle}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="ml-auto">
                      Switch to Annual Billing
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Switch to Annual Billing</DialogTitle>
                      <DialogDescription>
                        Save 16% by switching to annual billing. Your new plan will start immediately.
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4 py-4">
                      <div className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold capitalize">{subscription.plan} - Annual</h4>
                            <p className="text-sm text-muted-foreground">$490 per year</p>
                          </div>
                          <Badge className="bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-400">
                            Save 16%
                          </Badge>
                        </div>
                      </div>
                      
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Important</AlertTitle>
                        <AlertDescription>
                          You'll be charged immediately for the annual plan. Any unused portion of your current billing period will be prorated and credited to your account.
                        </AlertDescription>
                      </Alert>
                    </div>
                    
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setIsChangingBillingCycle(false)}
                        disabled={isProcessingAction && processingActionType === "billing"}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={() => handleBillingCycleChange("yearly")}
                        disabled={isProcessingAction && processingActionType === "billing"}
                      >
                        {isProcessingAction && processingActionType === "billing"
                          ? "Switching..."
                          : "Switch to Annual Billing"
                        }
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
              
              {subscription.billingCycle === "yearly" && (
                <Dialog open={isChangingBillingCycle} onOpenChange={setIsChangingBillingCycle}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="ml-auto">
                      Switch to Monthly Billing
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Switch to Monthly Billing</DialogTitle>
                      <DialogDescription>
                        You'll be switched to monthly billing at the end of your current annual billing period.
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4 py-4">
                      <div className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold capitalize">{subscription.plan} - Monthly</h4>
                            <p className="text-sm text-muted-foreground">$49 per month</p>
                          </div>
                        </div>
                      </div>
                      
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Please Note</AlertTitle>
                        <AlertDescription>
                          This change will take effect at the end of your current billing period on {format(subscription.currentPeriodEnd, "MMMM dd, yyyy")}.
                        </AlertDescription>
                      </Alert>
                    </div>
                    
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setIsChangingBillingCycle(false)}
                        disabled={isProcessingAction && processingActionType === "billing"}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={() => handleBillingCycleChange("monthly")}
                        disabled={isProcessingAction && processingActionType === "billing"}
                      >
                        {isProcessingAction && processingActionType === "billing"
                          ? "Switching..."
                          : "Switch to Monthly Billing"
                        }
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Subscription Status */}
      {subscription.status === "canceled" ? (
        <Card className="border-destructive/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-destructive">Subscription Canceled</CardTitle>
            <CardDescription>
              Your subscription has been canceled and will end on {format(subscription.currentPeriodEnd, "MMMM dd, yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="text-sm">
                You'll still have access to all {subscription.plan} plan features until your subscription ends.
              </div>
              <Button 
                onClick={handleReactivate}
                disabled={isProcessingAction && processingActionType === "reactivate"}
              >
                {isProcessingAction && processingActionType === "reactivate"
                  ? "Reactivating..."
                  : "Reactivate Subscription"
                }
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Manage Subscription</CardTitle>
            <CardDescription>
              Cancel or make changes to your subscription
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 justify-between">
              <div className="space-y-1">
                <div className="font-medium">Auto-renew</div>
                <div className="text-sm text-muted-foreground">
                  Your subscription will {subscription.autoRenew ? "" : "not "}
                  automatically renew on {format(subscription.currentPeriodEnd, "MMMM dd, yyyy")}
                </div>
              </div>
              
              {subscription.autoRenew ? (
                <AlertDialog open={isCancelling} onOpenChange={setIsCancelling}>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="text-destructive border-destructive hover:bg-destructive/10">
                      <X className="mr-2 h-4 w-4" /> Cancel Subscription
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Cancel your subscription?</AlertDialogTitle>
                      <AlertDialogDescription>
                        You'll continue to have access to all {subscription.plan} plan features until your current billing period ends on {format(subscription.currentPeriodEnd, "MMMM dd, yyyy")}.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="py-4">
                      <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
                        <h4 className="mb-2 font-medium">What happens when you cancel:</h4>
                        <ul className="list-inside list-disc text-sm space-y-1">
                          <li>Your subscription will remain active until {format(subscription.currentPeriodEnd, "MMMM dd, yyyy")}</li>
                          <li>You won't be billed again after this period ends</li>
                          <li>Your account will be downgraded to the Free plan</li>
                          <li>You can reactivate your subscription at any time before the period ends</li>
                        </ul>
                      </div>
                    </div>
                    <AlertDialogFooter>
                      <AlertDialogCancel disabled={isProcessingAction && processingActionType === "cancel"}>
                        Keep Subscription
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={(e) => {
                          e.preventDefault()
                          handleCancellation()
                        }}
                        className="bg-destructive hover:bg-destructive/90"
                        disabled={isProcessingAction && processingActionType === "cancel"}
                      >
                        {isProcessingAction && processingActionType === "cancel"
                          ? "Cancelling..."
                          : "Cancel Subscription"
                        }
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : (
                <Button 
                  onClick={handleReactivate}
                  disabled={isProcessingAction && processingActionType === "reactivate"}
                >
                  {isProcessingAction && processingActionType === "reactivate"
                    ? "Reactivating..."
                    : "Reactivate Subscription"
                  }
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}