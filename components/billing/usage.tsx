"use client"

import { useState } from "react"
import { motion, useReducedMotion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from "recharts"
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent
} from "@/components/ui/chart"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Download, Info, AlertCircle, Zap } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { format, subDays, startOfMonth, endOfMonth } from "date-fns"

// Generate mock usage data for charts
const generateDailyData = (days: number, maxInteractions: number = 1000, percentUsed: number = 0.7) => {
  return Array.from({ length: days }).map((_, i) => {
    const date = subDays(new Date(), days - i - 1)
    const interactions = Math.floor(Math.random() * (maxInteractions * percentUsed) * 0.8) + 
      Math.floor(maxInteractions * percentUsed * 0.2)
    
    return {
      date: format(date, "MMM dd"),
      interactions,
      limit: maxInteractions
    }
  })
}

const generateMonthlyData = (months: number = 6, maxInteractions: number = 30000) => {
  return Array.from({ length: months }).map((_, i) => {
    const monthName = format(subDays(new Date(), (months - i - 1) * 30), "MMM")
    const percentUsed = 0.3 + Math.random() * 0.5 // Between 30% and 80% usage
    
    return {
      name: monthName,
      interactions: Math.floor(maxInteractions * percentUsed),
      limit: maxInteractions
    }
  })
}

const generateHourlyData = () => {
  return Array.from({ length: 24 }).map((_, hour) => {
    const baseAmount = 5 + Math.random() * 20
    const peak1 = hour === 10 ? 40 : 0 // Morning peak
    const peak2 = hour === 15 ? 50 : 0 // Afternoon peak
    const peak3 = hour === 19 ? 35 : 0 // Evening peak
    const value = Math.floor(baseAmount + peak1 + peak2 + peak3)
    
    return {
      hour: `${hour}:00`,
      value
    }
  })
}

export function BillingUsage() {
  const [timeframe, setTimeframe] = useState("7d")
  const [activeTab, setActiveTab] = useState("overview")
  const prefersReducedMotion = useReducedMotion()
  
  // Mock data for charts based on selected timeframe
  const dailyData = (() => {
    switch(timeframe) {
      case "7d": return generateDailyData(7)
      case "30d": return generateDailyData(30)
      case "90d": return generateDailyData(90, 1000, 0.65)
      default: return generateDailyData(7)
    }
  })()
  
  const monthlyData = generateMonthlyData()
  const hourlyData = generateHourlyData()
  
  // Calculate current usage metrics
  const currentUsage = {
    aiInteractions: {
      used: dailyData.reduce((sum, day) => sum + day.interactions, 0),
      total: dailyData.reduce((sum, day) => sum + day.limit, 0),
      percentUsed: 0
    },
    agents: {
      used: 2,
      total: 3
    },
    integrations: {
      used: 2,
      total: 3
    },
    dataRetention: "30 days"
  }
  
  currentUsage.aiInteractions.percentUsed = 
    Math.round((currentUsage.aiInteractions.used / currentUsage.aiInteractions.total) * 100)
  
  // Time periods for select
  const timePeriods = [
    { label: "Last 7 days", value: "7d" },
    { label: "Last 30 days", value: "30d" },
    { label: "Last 90 days", value: "90d" }
  ]

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-xl font-semibold">Usage Metrics</h3>
          <p className="text-sm text-muted-foreground">
            Monitor your usage and resource consumption
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              {timePeriods.map(period => (
                <SelectItem key={period.value} value={period.value}>
                  {period.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ai-interactions">AI Interactions</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          <motion.div 
            className="grid gap-6 md:grid-cols-2 lg:grid-cols-4"
            initial={prefersReducedMotion ? { opacity: 1 } : "hidden"}
            animate={prefersReducedMotion ? { opacity: 1 } : "visible"}
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1
                }
              }
            }}
          >
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>AI Interactions Used</CardDescription>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-2xl">
                      {currentUsage.aiInteractions.used.toLocaleString()}
                    </CardTitle>
                    <Badge variant="outline" className="px-2">
                      {currentUsage.aiInteractions.percentUsed}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    of {currentUsage.aiInteractions.total.toLocaleString()} interactions
                  </div>
                  <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-muted">
                    <div 
                      className="h-full bg-primary" 
                      style={{ width: `${currentUsage.aiInteractions.percentUsed}%` }}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>Active Agents</CardDescription>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-2xl">
                      {currentUsage.agents.used} / {currentUsage.agents.total}
                    </CardTitle>
                    <Badge variant="outline" className="px-2">
                      {Math.round((currentUsage.agents.used / currentUsage.agents.total) * 100)}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    of your Pro plan allocation
                  </div>
                  <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-muted">
                    <div 
                      className="h-full bg-primary" 
                      style={{ width: `${(currentUsage.agents.used / currentUsage.agents.total) * 100}%` }}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>Platform Integrations</CardDescription>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-2xl">
                      {currentUsage.integrations.used} / {currentUsage.integrations.total}
                    </CardTitle>
                    <Badge variant="outline" className="px-2">
                      {Math.round((currentUsage.integrations.used / currentUsage.integrations.total) * 100)}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    of your Pro plan allocation
                  </div>
                  <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-muted">
                    <div 
                      className="h-full bg-primary" 
                      style={{ width: `${(currentUsage.integrations.used / currentUsage.integrations.total) * 100}%` }}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div variants={cardVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>Data Retention</CardDescription>
                  <CardTitle className="text-2xl">{currentUsage.dataRetention}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    Pro plan includes 30 days retention
                  </div>
                  <div className="mt-2 flex items-center gap-1 text-xs">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span>Data available since {format(subDays(new Date(), 30), "MMMM d, yyyy")}</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>

          {/* Daily Usage Chart */}
          <Card>
            <CardHeader>
              <CardTitle>AI Interactions - Daily Usage</CardTitle>
              <CardDescription>
                Your AI interaction usage over the past {timeframe === "7d" ? "week" : timeframe === "30d" ? "month" : "3 months"}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="h-[300px] w-full">
                <ChartContainer 
                  config={{
                    interactions: { label: "AI Interactions", color: "var(--primary)" },
                    limit: { label: "Daily Limit", color: "var(--muted-foreground)" }
                  }}
                >
                  <AreaChart data={dailyData} margin={{ top: 20, right: 30, left: 20, bottom: 30 }}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis 
                      dataKey="date" 
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      tickMargin={10}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      axisLine={false}
                      tickFormatter={(value) => `${value}`}
                    />
                    <ChartTooltip content={<ChartTooltipContent nameKey="dataKey" />} />
                    <Area 
                      type="monotone" 
                      dataKey="interactions" 
                      name="interactions"
                      stroke="var(--primary)" 
                      fill="var(--primary)" 
                      fillOpacity={0.2}
                      strokeWidth={2} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="limit" 
                      name="limit"
                      stroke="var(--muted-foreground)" 
                      fill="transparent"
                      strokeDasharray="5 5"
                      strokeWidth={1.5} 
                    />
                  </AreaChart>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
          
          {/* Usage by Hour of the Day */}
          <Card>
            <CardHeader>
              <CardTitle>AI Interactions - Usage by Hour</CardTitle>
              <CardDescription>
                Distribution of AI interactions across the day (average for selected period)
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="h-[300px] w-full">
                <ChartContainer 
                  config={{
                    value: { label: "Interactions", color: "var(--primary)" }
                  }}
                >
                  <BarChart data={hourlyData} margin={{ top: 20, right: 30, left: 20, bottom: 30 }}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" vertical={false} />
                    <XAxis 
                      dataKey="hour" 
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      tickMargin={10}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      axisLine={false}
                      tickFormatter={(value) => `${value}`}
                    />
                    <ChartTooltip content={<ChartTooltipContent nameKey="dataKey" />} />
                    <Bar
                      dataKey="value" 
                      name="value"
                      fill="var(--primary)" 
                      radius={[4, 4, 0, 0]} 
                    />
                  </BarChart>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
          
          {/* Monthly Usage History */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Usage History</CardTitle>
              <CardDescription>AI interactions over the past 6 months</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="h-[300px] w-full">
                <ChartContainer 
                  config={{
                    interactions: { label: "AI Interactions", color: "var(--primary)" },
                    limit: { label: "Monthly Limit", color: "var(--muted-foreground)" }
                  }}
                >
                  <BarChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 30 }}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" vertical={false} />
                    <XAxis 
                      dataKey="name" 
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      tickMargin={10}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      axisLine={false}
                      tickFormatter={(value) => `${value/1000}k`}
                    />
                    <ChartTooltip content={<ChartTooltipContent nameKey="dataKey" />} />
                    <Bar
                      dataKey="interactions"
                      name="interactions"
                      fill="var(--primary)" 
                      radius={[4, 4, 0, 0]} 
                    />
                    <Bar
                      dataKey="limit"
                      name="limit"
                      fill="rgba(0,0,0,0.1)" 
                      fillOpacity={0.2}
                      radius={[4, 4, 0, 0]} 
                    />
                  </BarChart>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
          
          {/* Usage Notifications */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Usage Insights</AlertTitle>
            <AlertDescription>
              {currentUsage.aiInteractions.percentUsed > 80 ? (
                <>
                  <strong>High usage detected.</strong> You've used {currentUsage.aiInteractions.percentUsed}% of your AI interactions
                  for this billing period. Consider upgrading your plan if you need more capacity.
                </>
              ) : (
                <>
                  You're currently using your resources efficiently. Based on your current 
                  usage patterns, your Pro plan is well-suited for your needs.
                </>
              )}
            </AlertDescription>
          </Alert>
          
          {/* Upgrade Options */}
          {currentUsage.aiInteractions.percentUsed > 80 && (
            <Card className="bg-muted/30">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="mr-2 h-5 w-5 text-primary" />
                  Need More Resources?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                  <p className="text-sm">
                    Upgrade to the Enterprise plan to get unlimited AI interactions,
                    more agents, and longer data retention.
                  </p>
                  <Button>Upgrade Plan</Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="ai-interactions">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>AI Interactions Details</CardTitle>
              <CardDescription>Detailed breakdown of your AI interaction usage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">Current Period Usage</div>
                    <div className="flex items-baseline space-x-2">
                      <span className="text-3xl font-bold">{currentUsage.aiInteractions.used.toLocaleString()}</span>
                      <span className="text-muted-foreground">of {currentUsage.aiInteractions.total.toLocaleString()}</span>
                    </div>
                    <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
                      <div 
                        className="h-full bg-primary" 
                        style={{ width: `${currentUsage.aiInteractions.percentUsed}%` }}
                      />
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {currentUsage.aiInteractions.percentUsed}% used
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">Daily Limit</div>
                    <div className="text-3xl font-bold">1,000</div>
                    <div className="text-xs text-muted-foreground">
                      Reset daily at midnight UTC
                    </div>
                  </div>
                </div>

                <div className="h-[400px] w-full">
                  <ChartContainer 
                    config={{
                      interactions: { label: "AI Interactions", color: "var(--primary)" },
                      limit: { label: "Daily Limit", color: "var(--muted-foreground)" }
                    }}
                  >
                    <AreaChart data={dailyData} margin={{ top: 20, right: 30, left: 20, bottom: 30 }}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis 
                        dataKey="date" 
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        tickMargin={10}
                      />
                      <YAxis 
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${value}`}
                      />
                      <ChartTooltip content={<ChartTooltipContent nameKey="dataKey" />} />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="interactions" 
                        name="interactions"
                        stroke="var(--primary)" 
                        fill="var(--primary)" 
                        fillOpacity={0.2}
                        strokeWidth={2} 
                      />
                      <Area 
                        type="monotone" 
                        dataKey="limit" 
                        name="limit"
                        stroke="var(--muted-foreground)" 
                        fill="transparent"
                        strokeDasharray="5 5"
                        strokeWidth={1.5} 
                      />
                    </AreaChart>
                  </ChartContainer>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Hourly Distribution</CardTitle>
                <CardDescription>When your AI interactions occur</CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="h-[300px] w-full">
                  <ChartContainer 
                    config={{
                      value: { label: "Interactions", color: "var(--primary)" }
                    }}
                  >
                    <BarChart data={hourlyData} margin={{ top: 20, right: 30, left: 20, bottom: 30 }}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" vertical={false} />
                      <XAxis 
                        dataKey="hour" 
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        tickMargin={10}
                      />
                      <YAxis 
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${value}`}
                      />
                      <ChartTooltip content={<ChartTooltipContent nameKey="dataKey" />} />
                      <Bar
                        dataKey="value" 
                        name="value"
                        fill="var(--primary)" 
                        radius={[4, 4, 0, 0]} 
                      />
                    </BarChart>
                  </ChartContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Usage Tips</CardTitle>
                <CardDescription>Make the most of your AI interactions</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-sm">
                  <li className="flex items-start">
                    <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-primary/10 text-primary">
                      1
                    </div>
                    <span>Optimize your agent personalities to be more concise to reduce token usage.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-primary/10 text-primary">
                      2
                    </div>
                    <span>Consider batching similar tasks together to minimize redundant interactions.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-primary/10 text-primary">
                      3
                    </div>
                    <span>Create standardized response templates for common community questions.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-0.5 h-5 w-5 flex items-center justify-center rounded-full bg-primary/10 text-primary">
                      4
                    </div>
                    <span>Use custom training to improve AI accuracy and reduce follow-up interactions.</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  View Usage Guide
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="agents">
          <Card>
            <CardHeader>
              <CardTitle>Agent Usage</CardTitle>
              <CardDescription>Details about your AI agent usage</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Agent usage details coming soon.</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle>Integration Usage</CardTitle>
              <CardDescription>Details about your platform integration usage</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Integration usage details coming soon.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}