import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";

export interface BlogPostCardProps {
  title: string;
  summary: string;
  date: string;
  author: string;
  authorImage?: string;
  href: string;
  className?: string;
}

export function BlogPostCard({ title, summary, date, author, authorImage, href, className }: BlogPostCardProps) {
  return (
    <Link href={href} className={cn("block group", className)}>
      <Card className="transition-shadow group-hover:shadow-lg h-full">
        <CardHeader>
          <CardTitle className="text-lg group-hover:text-primary transition-colors">{title}</CardTitle>
          <CardDescription className="flex flex-wrap items-center gap-2 mt-1">
            {authorImage && (
              <div className="h-6 w-6 rounded-full overflow-hidden relative mr-1">
                <Image 
                  src={authorImage} 
                  alt={`${author}'s profile`} 
                  fill 
                  className="object-cover"
                />
              </div>
            )}
            <span className="text-xs text-muted-foreground">{date}</span>
            <span className="text-xs text-muted-foreground">by {author}</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground line-clamp-3">{summary}</p>
        </CardContent>
      </Card>
    </Link>
  );
}
