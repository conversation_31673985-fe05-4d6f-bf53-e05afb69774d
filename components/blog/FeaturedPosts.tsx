import React from "react";
import Link from "next/link";
import { BlogPostCard } from "./BlogPostCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

// This would typically come from a CMS or API
const featuredPosts = [
  {
    title: "Introducing Commuza: The Future of Communication",
    summary: "Learn about how Commuza is revolutionizing the way teams communicate and collaborate.",
    date: "April 28, 2025",
    author: "<PERSON>",
    authorImage: "/profile-1.jpg",
    href: "/blog/introducing-commuza",
  },
  {
    title: "5 Ways to Improve Team Productivity",
    summary: "Discover proven strategies to boost your team's efficiency and productivity with Commuza.",
    date: "April 20, 2025",
    author: "<PERSON>",
    authorImage: "/profile-2.jpg",
    href: "/blog/improve-team-productivity",
  },
  {
    title: "The Evolution of Workplace Communication",
    summary: "A deep dive into how workplace communication has evolved and where it's headed.",
    date: "April 15, 2025",
    author: "<PERSON>",
    authorImage: "/profile-3.jpg",
    href: "/blog/evolution-of-workplace-communication",
  },
];

export function FeaturedPosts() {
  return (
    <section className="py-16 bg-muted/40">
      <div className="container max-w-6xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10">
          <div>
            <h2 className="text-3xl font-bold mb-2">Featured Posts</h2>
            <p className="text-muted-foreground text-lg">
              Latest insights and updates from our team
            </p>
          </div>
          <Link href="/blog" className="mt-4 md:mt-0">
            <Button variant="outline" className="gap-2">
              View all posts
              <ArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredPosts.map((post, index) => (
            <BlogPostCard
              key={index}
              title={post.title}
              summary={post.summary}
              date={post.date}
              author={post.author}
              authorImage={post.authorImage}
              href={post.href}
            />
          ))}
        </div>
      </div>
    </section>
  );
}