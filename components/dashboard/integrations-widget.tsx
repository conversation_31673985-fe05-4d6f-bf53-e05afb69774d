"use client";

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PlatformIntegrations } from "@/components/dashboard/platform-integrations";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export function IntegrationsWidget() {
  return (
    <Card className="col-span-full xl:col-span-6">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Platform Integrations</CardTitle>
            <CardDescription className="mt-1">
              Manage your connected community platforms
            </CardDescription>
          </div>
          <Button size="sm" variant="outline" className="gap-1.5 h-9">
            <Plus className="h-4 w-4" /> Add Integration
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <PlatformIntegrations className="mt-2" />
        <div className="mt-4 flex flex-row items-center justify-between text-sm text-muted-foreground">
          <span>3 active integrations</span>
          <span className="text-xs px-2 py-1 rounded-full bg-muted">Auto-sync enabled</span>
        </div>
      </CardContent>
    </Card>
  );
}