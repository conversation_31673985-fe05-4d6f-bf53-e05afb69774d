"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

const data = [
  {
    name: "<PERSON>",
    conversations: 120,
    moderation: 15,
  },
  {
    name: "<PERSON><PERSON>",
    conversations: 160,
    moderation: 20,
  },
  {
    name: "<PERSON><PERSON>",
    conversations: 180,
    moderation: 22,
  },
  {
    name: "T<PERSON>",
    conversations: 190,
    moderation: 25,
  },
  {
    name: "<PERSON><PERSON>",
    conversations: 170,
    moderation: 18,
  },
  {
    name: "<PERSON><PERSON>",
    conversations: 110,
    moderation: 12,
  },
  {
    name: "<PERSON>",
    conversations: 90,
    moderation: 10,
  },
]

export function Overview() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data}>
        <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
        <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} tickFormatter={(value) => `${value}`} />
        <Tooltip />
        <Bar dataKey="conversations" fill="currentColor" radius={[4, 4, 0, 0]} className="fill-primary" />
        <Bar dataKey="moderation" fill="currentColor" radius={[4, 4, 0, 0]} className="fill-primary/30" />
      </BarChart>
    </ResponsiveContainer>
  )
}
