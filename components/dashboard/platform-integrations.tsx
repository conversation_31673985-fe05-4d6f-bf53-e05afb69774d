"use client";

import { forwardRef, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { motion, useReducedMotion } from "framer-motion";

import { cn } from "@/lib/utils";
import { AnimatedBeam } from "@/components/magicui/animated-beam";

const PlatformIcon = forwardRef<
  HTMLDivElement,
  { 
    className?: string; 
    children?: React.ReactNode;
    src: string;
    alt: string;
    size?: "sm" | "md" | "lg";
  }
>(({ className, children, src, alt, size = "md" }, ref) => {
  const sizeClasses = {
    sm: "h-10 w-10 p-1.5",
    md: "h-12 w-12 p-2",
    lg: "h-16 w-16 p-2.5",
  };

  const imgSizeClasses = {
    sm: "h-7 w-7",
    md: "h-8 w-8",
    lg: "h-11 w-11",
  };

  return (
    <div
      ref={ref}
      className={cn(
        "z-10 flex items-center justify-center rounded-full border border-border bg-card shadow-md dark:border-border/50",
        sizeClasses[size],
        className,
      )}
    >
      <Image 
        src={src} 
        alt={alt}
        width={32}
        height={32}
        className={cn(imgSizeClasses[size])}
      />
      {children}
    </div>
  );
});

PlatformIcon.displayName = "PlatformIcon";

export function PlatformIntegrations({
  className,
}: {
  className?: string;
}) {
  const prefersReducedMotion = useReducedMotion();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Wait for component to be mounted to properly calculate positions
    const timer = setTimeout(() => {
      setIsMounted(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const containerRef = useRef<HTMLDivElement>(null);
  const commuzaLogoRef = useRef<HTMLDivElement>(null);
  const discordRef = useRef<HTMLDivElement>(null);
  const slackRef = useRef<HTMLDivElement>(null);
  const telegramRef = useRef<HTMLDivElement>(null);

  return (
    <div
      className={cn(
        "relative flex h-[420px] w-full items-center justify-center overflow-hidden rounded-xl border bg-gradient-to-br from-background to-muted/20 p-8",
        className,
      )}
      ref={containerRef}
    >
      <div className="flex h-full w-full flex-col items-center justify-between">
        <div className="flex flex-row items-center justify-center gap-12 sm:gap-16 md:gap-24 lg:gap-32 mt-4">
          <PlatformIcon 
            ref={discordRef} 
            src="/discord_icon.svg" 
            alt="Discord"
            size="lg"
            className="transition-transform duration-300 hover:scale-105"
          />
          <PlatformIcon 
            ref={slackRef} 
            src="/slack_icon.svg" 
            alt="Slack"
            size="lg"
            className="transition-transform duration-300 hover:scale-105"
          />
          <PlatformIcon 
            ref={telegramRef} 
            src="/telegram_icon.svg" 
            alt="Telegram"
            size="lg"
            className="transition-transform duration-300 hover:scale-105"
          />
        </div>

        <motion.div 
          className="flex flex-col items-center mt-4 mb-6"
          initial={{ y: 10 }}
          animate={{ 
            y: prefersReducedMotion ? 0 : [0, -5, 0] 
          }}
          transition={{
            duration: 3,
            ease: [0.16, 1, 0.3, 1],
            repeat: Infinity,
            repeatType: "reverse",
          }}
        >
          <div ref={commuzaLogoRef} className="h-20 w-20 md:h-24 md:w-24 bg-card rounded-full p-2 border border-border/30 shadow-lg flex items-center justify-center">
            <Image 
              src="/logo.svg" 
              alt="Commuza Logo" 
              width={96}
              height={96}
              className="h-full w-full"
              priority
            />
          </div>
          <span className="mt-4 text-center text-lg font-semibold text-foreground">
            Connected Integration Hub
          </span>
          <p className="mt-2 max-w-md text-center text-sm text-muted-foreground md:text-base">
            Seamless connections between your communities across multiple platforms
          </p>
        </motion.div>
      </div>

      {/* Animated Beams */}
      {isMounted && !prefersReducedMotion && commuzaLogoRef.current && discordRef.current && (
        <AnimatedBeam 
          containerRef={containerRef}
          fromRef={discordRef}
          toRef={commuzaLogoRef}
          pathColor="rgba(114, 137, 218, 0.15)"
          gradientStartColor="#7289da" 
          gradientStopColor="#7C3AED" 
          pathWidth={2}
          curvature={110}
          delay={0.2}
          startYOffset={5}
          endYOffset={-5}
          // Avoid overlapping by offsetting position
          startXOffset={-2}
        />
      )}
      
      {isMounted && !prefersReducedMotion && commuzaLogoRef.current && slackRef.current && (
        <AnimatedBeam 
          containerRef={containerRef}
          fromRef={slackRef}
          toRef={commuzaLogoRef}
          pathColor="rgba(74, 21, 75, 0.15)"
          gradientStartColor="#4A154B" 
          gradientStopColor="#7C3AED" 
          pathWidth={2}
          curvature={90}
          delay={0.8}
          // Adjust the starting and ending positions
          startYOffset={5}
          endYOffset={-5}
        />
      )}
      
      {isMounted && !prefersReducedMotion && commuzaLogoRef.current && telegramRef.current && (
        <AnimatedBeam 
          containerRef={containerRef}
          fromRef={telegramRef}
          toRef={commuzaLogoRef}
          pathColor="rgba(0, 136, 204, 0.15)"
          gradientStartColor="#0088cc" 
          gradientStopColor="#7C3AED" 
          pathWidth={2}
          curvature={75}
          delay={0.5}
          // Adjust the starting and ending positions
          startYOffset={5}
          endYOffset={-5}
          startXOffset={2}
        />
      )}
    </div>
  );
}