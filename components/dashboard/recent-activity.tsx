"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, useReducedMotion } from "framer-motion"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertTriangle,
  MoreVertical,
  MessageSquare,
  Users,
  Bot,
  AlertCircle,
  CheckCircle,
  Clock,
  Shield,
  MailQuestion
} from "lucide-react"

// Type definition for activities
type PlatformType = "telegram" | "discord" | "facebook" | "slack" | "system"
type ActivityType = "flagged" | "moderation" | "suggestion" | "growth" | "support" | "conversation" | "resolved"
type SeverityType = "low" | "medium" | "high" | "critical"

interface Activity {
  id: number
  type: ActivityType
  platform: PlatformType
  agent: string
  agentInitials: string
  content: string
  group?: string
  user?: string
  userInitials?: string
  time: string
  severity?: SeverityType
  status?: "pending" | "reviewing" | "resolved"
}

const activities: Activity[] = [
  {
    id: 1,
    type: "flagged",
    platform: "telegram",
    agent: "ContentGuardian",
    agentInitials: "CG",
    content: "Flagged post containing potential spam links in Telegram group",
    group: "Product Enthusiasts",
    user: "Alex T.",
    userInitials: "AT",
    time: "5 minutes ago",
    severity: "medium",
    status: "reviewing"
  },
  {
    id: 2,
    type: "flagged",
    platform: "discord",
    agent: "ToxicityShield",
    agentInitials: "TS",
    content: "Detected harassment in message directed at community member",
    group: "Gaming Community",
    user: "MemberID#4281",
    userInitials: "MI",
    time: "17 minutes ago",
    severity: "high",
    status: "reviewing"
  },
  {
    id: 3,
    type: "suggestion",
    platform: "facebook",
    agent: "GrowthGuide",
    agentInitials: "GG",
    content: "Recommended topic for new poll based on trending discussions",
    group: "Digital Marketing Experts",
    time: "43 minutes ago"
  },
  {
    id: 4,
    type: "flagged",
    platform: "facebook",
    agent: "PrivacyProtect",
    agentInitials: "PP",
    content: "Identified post containing personal information disclosure",
    group: "Neighborhood Watch",
    user: "Jennifer S.",
    userInitials: "JS",
    time: "1 hour ago",
    severity: "critical",
    status: "pending"
  },
  {
    id: 5,
    type: "moderation",
    platform: "discord",
    agent: "LanguageMonitor",
    agentInitials: "LM",
    content: "Auto-removed message with offensive language in #help channel",
    group: "Developer Hub",
    user: "CodeMaster#1337",
    userInitials: "CM",
    time: "2 hours ago",
    severity: "medium",
    status: "resolved"
  },
  {
    id: 6,
    type: "growth",
    platform: "slack",
    agent: "EngagementBoost",
    agentInitials: "EB",
    content: "Initiated welcome thread for 5 new members with relevant resources",
    group: "Startup Founders Network",
    time: "3 hours ago"
  },
  {
    id: 7,
    type: "flagged",
    platform: "discord",
    agent: "ScamDetector",
    agentInitials: "SD",
    content: "Flagged potential phishing attempt in direct messages",
    group: "Crypto Discussions",
    user: "BlockchainBob#8901",
    userInitials: "BB", 
    time: "4 hours ago",
    severity: "high",
    status: "pending"
  }
]

export function RecentActivity() {
  const [displayedActivities, setDisplayedActivities] = useState<Activity[]>(activities)
  const prefersReducedMotion = useReducedMotion()

  const getSeverityColor = (severity?: SeverityType) => {
    switch (severity) {
      case "low":
        return "bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-400"
      case "medium":
        return "bg-amber-50 text-amber-700 dark:bg-amber-950 dark:text-amber-400"
      case "high":
        return "bg-red-50 text-red-700 dark:bg-red-950 dark:text-red-400"
      case "critical":
        return "bg-rose-50 text-rose-700 dark:bg-rose-950 dark:text-rose-400"
      default:
        return "bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
    }
  }

  const getPlatformIcon = (platform: PlatformType) => {
    switch (platform) {
      case "telegram":
        return (
          <div className="relative w-3 h-3 flex items-center justify-center">
            <Image 
              src="/telegram_icon.svg" 
              alt="Telegram" 
              width={12} 
              height={12} 
              className="object-contain" 
            />
          </div>
        )
      case "discord":
        return (
          <div className="relative w-3 h-3 flex items-center justify-center">
            <Image 
              src="/discord_icon.svg" 
              alt="Discord" 
              width={12} 
              height={12} 
              className="object-contain" 
            />
          </div>
        )
      case "facebook":
        return (
          <div className="relative w-3 h-3 flex items-center justify-center">
            <Image 
              src="/facebook_icon.svg" 
              alt="Facebook" 
              width={12} 
              height={12} 
              className="object-contain" 
            />
          </div>
        )
      case "slack":
        return (
          <div className="relative w-3 h-3 flex items-center justify-center">
            <Image 
              src="/slack_icon.svg" 
              alt="Slack" 
              width={12} 
              height={12} 
              className="object-contain" 
            />
          </div>
        )
      default:
        return <Bot className="h-3 w-3" />
    }
  }

  const getPlatformLabel = (platform: PlatformType) => {
    switch (platform) {
      case "telegram":
        return "Telegram"
      case "discord":
        return "Discord"
      case "facebook":
        return "Facebook"
      case "slack":
        return "Slack"
      default:
        return "System"
    }
  }

  const getTypeIcon = (type: ActivityType) => {
    switch (type) {
      case "flagged":
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      case "moderation":
        return <Shield className="h-4 w-4 text-red-500" />
      case "suggestion":
        return <MailQuestion className="h-4 w-4 text-blue-500" />
      case "growth":
        return <Users className="h-4 w-4 text-green-500" />
      case "support":
        return <MessageSquare className="h-4 w-4 text-violet-500" />
      case "resolved":
        return <CheckCircle className="h-4 w-4 text-emerald-500" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-500" />
    }
  }
  
  const getStatusBadge = (status?: string) => {
    if (!status) return null
    
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="ml-2 bg-amber-50 text-amber-700 dark:bg-amber-950 dark:text-amber-400">
            <Clock className="mr-1 h-3 w-3" /> Pending
          </Badge>
        )
      case "reviewing":
        return (
          <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-400">
            <AlertCircle className="mr-1 h-3 w-3" /> Reviewing
          </Badge>
        )
      case "resolved":
        return (
          <Badge variant="outline" className="ml-2 bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-400">
            <CheckCircle className="mr-1 h-3 w-3" /> Resolved
          </Badge>
        )
      default:
        return null
    }
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 220,
        damping: 20
      }
    }
  }

  return (
    <motion.div 
      className="space-y-4 overflow-hidden"
      initial={prefersReducedMotion ? { opacity: 1 } : "hidden"}
      animate={prefersReducedMotion ? { opacity: 1 } : "visible"}
      variants={containerVariants}
    >
      {displayedActivities.map((activity) => (
        <motion.div 
          key={activity.id} 
          variants={itemVariants}
          className="relative rounded-lg border p-4 hover:bg-muted/50 transition-colors overflow-hidden"
        >
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <Avatar className="h-9 w-9 border-2 border-primary/10">
                <AvatarImage src={`/placeholder-logo.svg`} alt={activity.agent} />
                <AvatarFallback className="bg-primary/10 text-primary">{activity.agentInitials}</AvatarFallback>
              </Avatar>
            </div>
            
            <div className="flex-grow space-y-1.5 min-w-0">
              <div className="flex flex-wrap items-center justify-between gap-2">
                <div className="flex flex-wrap items-center gap-1.5 max-w-full">
                  <span className="flex-shrink-0">{getTypeIcon(activity.type)}</span>
                  <span className="text-sm font-medium truncate max-w-[150px] sm:max-w-none">{activity.agent}</span>
                  <Badge variant="outline" className="text-[10px] flex items-center px-2 py-0 flex-shrink-0">
                    {getPlatformIcon(activity.platform)}
                    <span className="ml-1">{getPlatformLabel(activity.platform)}</span>
                  </Badge>
                  {activity.severity && (
                    <Badge className={`text-[10px] flex-shrink-0 ${getSeverityColor(activity.severity)}`}>
                      {activity.severity.charAt(0).toUpperCase() + activity.severity.slice(1)}
                    </Badge>
                  )}
                  {getStatusBadge(activity.status)}
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 flex-shrink-0">
                      <span className="sr-only">Open menu</span>
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>View details</DropdownMenuItem>
                    <DropdownMenuItem>Mark as resolved</DropdownMenuItem>
                    <DropdownMenuItem>Assign to team</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2">{activity.content}</p>
              
              <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-xs text-muted-foreground">
                {activity.group && (
                  <span className="flex items-center">
                    <Users className="mr-1 h-3 w-3 flex-shrink-0" />
                    <span className="truncate max-w-[120px] sm:max-w-none">{activity.group}</span>
                  </span>
                )}
                
                {activity.user && (
                  <span className="flex items-center">
                    <span className="mr-1 relative flex h-3 w-3 items-center justify-center flex-shrink-0">
                      <span className="absolute inset-0 rounded-full bg-primary/10" />
                      <span className="text-[8px] font-bold text-primary">{activity.userInitials}</span>
                    </span>
                    <span className="truncate max-w-[120px] sm:max-w-none">{activity.user}</span>
                  </span>
                )}
                
                <span className="flex items-center">
                  <Clock className="mr-1 h-3 w-3 flex-shrink-0" />
                  {activity.time}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </motion.div>
  )
}
