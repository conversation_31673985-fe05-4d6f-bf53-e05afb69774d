"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>hart3, Shield, Zap, MessageSquare, Settings, Users, Sparkles } from "lucide-react"
import { PlatformIntegrations } from "@/components/dashboard/platform-integrations"
import { useReducedMotion } from "framer-motion"
import { useEffect, useState } from "react"

const features = [
  {
    name: "AI-Powered Engagement",
    description:
      "Intelligent agents that respond to community questions, provide helpful resources, and keep conversations flowing.",
    icon: MessageSquare,
  },
  {
    name: "Smart Moderation",
    description:
      "Automatically detect and handle inappropriate content, spam, and rule violations to maintain a healthy community.",
    icon: Shield,
  },
  {
    name: "Cross-Platform Support",
    description:
      "Deploy your AI community managers across Discord, Slack, Reddit, Telegram, and more from a single dashboard.",
    icon: Zap,
  },
  {
    name: "Customizable Personalities",
    description:
      "Create unique AI agents with distinct personalities, tones, and behaviors that match your brand voice.",
    icon: Bot,
  },
  {
    name: "Analytics Dashboard",
    description:
      "Track engagement metrics, moderation actions, and community health with real-time insights and reports.",
    icon: BarChart3,
  },
  {
    name: "Easy Configuration",
    description: "No-code interface to set up and customize your AI community managers without technical expertise.",
    icon: Settings,
  },
  {
    name: "Community Growth Tools",
    description: "Proactive suggestions for content, events, and engagement strategies to grow your community.",
    icon: Users,
  },
  {
    name: "Continuous Learning",
    description: "AI agents that improve over time by learning from interactions and feedback from your community.",
    icon: Sparkles,
  },
]

export function LandingFeatures() {
  const prefersReducedMotion = useReducedMotion();
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  useEffect(() => {
    // Check screen size to adjust platform integration visibility
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 768);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <section id="features" className="container py-16 md:py-24 lg:py-32 space-y-24">
      <div className="relative mx-auto flex max-w-[58rem] flex-col items-center space-y-6 text-center">
        <div>
          <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">
            Powerful Features for Modern Communities
          </h2>
          <p className="mt-4 max-w-[85%] mx-auto leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            Everything you need to automate and enhance your community management across all platforms.
          </p>
        </div>
        
        {/* Platform Integration Visualization */}
        {isLargeScreen && (
          <div className="w-full max-w-[480px] h-[280px] md:h-[320px] mb-12">
            <PlatformIntegrations 
              className="border-none bg-gradient-to-b from-background/20 to-background/5 shadow-sm scale-90"
            />
          </div>
        )}
      </div>
      
      <div className="mx-auto grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 lg:gap-8 items-start">
        {features.map((feature) => (
          <div key={feature.name} className="flex flex-col items-center gap-2 text-center">
            <div className="flex h-14 w-14 items-center justify-center rounded-lg bg-primary/10">
              <feature.icon className="h-6 w-6 text-primary" />
            </div>
            <h3 className="font-semibold text-lg">{feature.name}</h3>
            <p className="text-sm text-muted-foreground">{feature.description}</p>
          </div>
        ))}
      </div>
    </section>
  )
}
