"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion, AnimatePresence, useReducedMotion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Send, Hash, Users, Search, Plus, Gift, Smile } from "lucide-react"
import { ThemeAwareLogo } from "@/components/ui/theme-aware-logo"
import { cn } from "@/lib/utils"

// Define message types
type MessageType = "user" | "ai"

interface Message {
  id: number
  type: MessageType
  content: string
  timestamp: Date
  isTyping?: boolean
  username?: string
  role?: string
  roleColor?: string
}

export function LandingHero() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      type: "ai",
      content: "👋 Hello everyone! I'm <PERSON><PERSON><PERSON><PERSON><PERSON>, your AI community manager. I'm active and monitoring this channel for sensitive content, spam, and helping with community questions. Feel free to chat - I'm here to help keep the conversation friendly and productive!",
      timestamp: new Date(Date.now() - 180000),
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      role: "BOT",
      roleColor: "bg-primary",
    },
    {
      id: 2,
      type: "user",
      content: "Hi everyone! Just joined the community. Looking forward to learning more about Commuza!",
      timestamp: new Date(Date.now() - 120000),
      username: "new_member",
    },
    {
      id: 3,
      type: "user",
      content: "Welcome @new_member! Glad to have you here. Have you checked out the documentation yet?",
      timestamp: new Date(Date.now() - 80000),
      username: "DevTeam",
    },
    {
      id: 4,
      type: "user",
      content: "This service is terrible, I hate everything about it! What a waste of money!",
      timestamp: new Date(Date.now() - 60000),
      username: "angry_user",
    },
  ])

  const [inputValue, setInputValue] = useState("")
  const [isAiTyping, setIsAiTyping] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const sectionRef = useRef<HTMLElement>(null)
  
  // Check for reduced motion preference
  const prefersReducedMotion = useReducedMotion()

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [messages])

  // Simulate AI responding to sensitive content after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAiTyping(true)

      // Simulate AI typing delay then add response
      const typingTimer = setTimeout(() => {
        setIsAiTyping(false)
        setMessages((prev) => [
          ...prev,
          {
            id: prev.length + 1,
            type: "ai",
            content: "⚠️ **Sensitive Content Alert**: I've detected potentially negative content in the last message. Please remember our community guidelines encourage constructive feedback. If you're experiencing issues, our support team would be happy to help resolve them!",
            timestamp: new Date(),
            username: "CommuzaBot",
            role: "BOT",
            roleColor: "bg-primary",
          },
        ])
      }, 1500)

      return () => clearTimeout(typingTimer)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Check content for sensitive phrases
  const checkForSensitiveContent = (content: string): boolean => {
    const sensitivePatterns = [
      /terrible/i,
      /hate/i, 
      /awful/i,
      /stupid/i,
      /waste/i,
      /useless/i,
      /\bfuck\b/i,
      /\bshit\b/i,
      /garbage/i,
    ]
    
    return sensitivePatterns.some(pattern => pattern.test(content))
  }

  // Handle sending a new message
  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    // Add user message
    const newUserMessage: Message = {
      id: messages.length + 1,
      type: "user",
      content: inputValue,
      timestamp: new Date(),
      username: "new_member",
    }

    setMessages((prev) => [...prev, newUserMessage])
    setInputValue("")

    // Check if message contains sensitive content
    const containsSensitiveContent = checkForSensitiveContent(inputValue)

    // Simulate AI typing
    setIsAiTyping(true)

    // Prepare AI response based on content
    let aiResponse = ""
    
    if (containsSensitiveContent) {
      aiResponse = "⚠️ **Sensitive Content Alert**: I've detected potentially inappropriate content in your message. Please remember our community guidelines and keep conversations respectful and constructive. If you have concerns, consider phrasing them more neutrally."
    } else if (inputValue.toLowerCase().includes("help")) {
      aiResponse = "I'm here to help! What specific questions do you have about our community or platform?"
    } else if (inputValue.toLowerCase().includes("feature") || inputValue.toLowerCase().includes("product")) {
      aiResponse = "Great question about our features! The latest updates include advanced analytics, custom AI training, and expanded platform integrations. Check out our changelog for more details."
    } else {
      aiResponse = "Thanks for your message! Is there anything specific I can help you with about Commuza?"
    }

    // Add AI response after a delay
    setTimeout(
      () => {
        setIsAiTyping(false)
        setMessages((prev) => [
          ...prev,
          {
            id: prev.length + 2,
            type: "ai",
            content: aiResponse,
            timestamp: new Date(),
            username: "CommuzaBot",
            role: "BOT",
            roleColor: "bg-primary",
          },
        ])
      },
      1500 + Math.random() * 1000,
    ) // Random delay between 1.5-2.5s for realism
  }

  // Mock data for Discord-like UI
  const servers = [
    { id: 1, name: "Commuza HQ", icon: "/logo.svg" },
    { id: 4, name: "Community", icon: "/solana.png" }
  ]

  const channels = [
    { id: 1, name: "welcome", category: "INFORMATION" },
    { id: 2, name: "announcements", category: "INFORMATION" },
    { id: 3, name: "general", category: "COMMUNITY", active: true },
    { id: 4, name: "help-desk", category: "COMMUNITY" },
    { id: 5, name: "feature-requests", category: "COMMUNITY" },
    { id: 6, name: "showcase", category: "COMMUNITY" }
  ]

  const members = [
    { id: 1, name: "CommuzaBot", status: "online", role: "BOT", image: "/logo.svg" },
    { id: 2, name: "Moderator", status: "online", role: "MOD", image: "/profile-1.jpg" },
    { id: 3, name: "DevTeam", status: "online", role: "DEV", image: "/profile-2.jpg" },
    { id: 4, name: "new_member", status: "online", role: "MEMBER", image: "/profile-5.jpg" },
    { id: 5, name: "angry_user", status: "online", role: "MEMBER", image: "/thief.jpg" },
    { id: 6, name: "supporter_1", status: "online", role: "MEMBER", image: "/profile-4.jpg" }
  ]

  return (
    <section className="relative py-20 md:py-32 overflow-hidden" ref={sectionRef}>
      <div className="container px-4 md:px-6 relative z-10">
        <div className="grid gap-6 lg:grid-cols-[1fr_600px] lg:gap-12 xl:grid-cols-[1fr_800px]">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <div className="flex justify-center mb-8 md:justify-start relative z-20">
                <ThemeAwareLogo size="lg" />
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                AI-Powered Community Managers for Modern Brands
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl">
                Deploy intelligent AI agents that automate engagement, moderation, and growth for your online
                communities across Discord, Slack, Telegram, and more.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Link href="/signup">
                <Button size="lg" className="gap-1.5">
                  Get Started <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="/demo">
                <Button size="lg" variant="outline">
                  Request Demo
                </Button>
              </Link>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="inline-block h-8 w-8 overflow-hidden rounded-full border-2 border-background">
                    <Image
                      src={`/profile-${i}.jpg`}
                      alt={`Community manager profile ${i}`}
                      width={32}
                      height={32}
                      className="h-full w-full object-cover"
                      priority={i === 1}
                    />
                  </div>
                ))}
              </div>
              <div className="text-muted-foreground">Trusted by 1,000+ community managers</div>
            </div>
          </div>

          {/* Discord-styled UI */}
          <motion.div
            className="w-full h-[600px] rounded-xl border bg-[#313338] shadow-xl overflow-hidden z-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: [0.16, 1, 0.3, 1] }}
          >
            <div className="flex h-full">
              {/* Server sidebar */}
              <div className="w-[72px] bg-[#1e1f22] flex flex-col items-center py-3 space-y-2">
                <div className="w-12 h-12 bg-[#35363a] rounded-2xl bg-primary flex items-center justify-center mb-2 cursor-pointer hover:rounded-xl transition-all duration-200">
                  <Image src="/discord_icon.svg" alt="Commuza" width={24} height={24} />
                </div>
                <div className="w-12 h-[2px] bg-[#35363a] rounded-full my-1"></div>
                {servers.map(server => (
                  <div 
                    key={server.id} 
                    className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center mb-2 cursor-pointer hover:rounded-2xl transition-all duration-200",
                      server.name === "Discord" ? "bg-[#5865F2]" : "bg-[#35363a]"
                    )}
                  >
                    <Image src={server.icon} alt={server.name} width={24} height={24} />
                  </div>
                ))}
              </div>

              {/* Channels sidebar */}
              <div className="w-[180px] bg-[#2b2d31] border-r border-[#35363a]">
                <div className="h-12 shadow-sm flex items-center px-4 border-b border-[#35363a]">
                  <h2 className="font-medium text-white truncate">Commuza Community</h2>
                </div>

                <div className="p-2 overflow-y-auto h-[calc(100%-48px)] scrollbar-thin scrollbar-thumb-[#202225] hover:scrollbar-thumb-[#2a2c31] scrollbar-track-transparent">
                  {/* Channel categories */}
                  <div className="mb-2">
                    <h3 className="text-xs font-semibold text-[#989aa2] px-2 py-1.5">INFORMATION</h3>
                    {channels
                      .filter(channel => channel.category === "INFORMATION")
                      .map(channel => (
                        <div 
                          key={channel.id} 
                          className={cn(
                            "flex items-center px-2 py-1.5 rounded cursor-pointer group",
                            channel.active ? "bg-[#404249] text-white" : "text-[#989aa2] hover:bg-[#35363a] hover:text-[#f2f3f5]"
                          )}
                        >
                          <Hash className="h-5 w-5 mr-1.5 text-[#989aa2] shrink-0" />
                          <span className="truncate">{channel.name}</span>
                        </div>
                      ))
                    }
                  </div>

                  <div>
                    <h3 className="text-xs font-semibold text-[#989aa2] px-2 py-1.5">COMMUNITY</h3>
                    {channels
                      .filter(channel => channel.category === "COMMUNITY")
                      .map(channel => (
                        <div 
                          key={channel.id} 
                          className={cn(
                            "flex items-center px-2 py-1.5 rounded cursor-pointer group",
                            channel.active ? "bg-[#404249] text-white" : "text-[#989aa2] hover:bg-[#35363a] hover:text-[#f2f3f5]"
                          )}
                        >
                          <Hash className="h-5 w-5 mr-1.5 text-[#989aa2] shrink-0" />
                          <span className="truncate">{channel.name}</span>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>

              {/* Chat area */}
              <div className="flex-1 flex flex-col bg-[#313338]">
                {/* Channel header */}
                <div className="h-12 border-b border-[#35363a] flex items-center justify-between px-4">
                  <div className="flex items-center">
                    <Hash className="h-5 w-5 mr-1.5 text-[#989aa2]" />
                    <span className="font-medium text-white">general</span>
                  </div>
                  <div className="flex items-center space-x-2 text-[#b5bac1]">
                    <button className="p-1.5 rounded hover:bg-[#35363a]">
                      <Users className="h-5 w-5" />
                    </button>
                    <button className="p-1.5 rounded hover:bg-[#35363a]">
                      <Search className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Messages area */}
                <div 
                  ref={chatContainerRef}
                  className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-[#202225] hover:scrollbar-thumb-[#2a2c31] scrollbar-track-transparent"
                >
                  <div className="p-4 space-y-4">
                    <AnimatePresence initial={false}>
                      {messages.map((message) => (
                        <motion.div
                          key={message.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.2 }}
                          className="flex items-start gap-3 group hover:bg-[#2e3035] p-1 rounded-md -ml-1 -mr-1"
                        >
                          {/* Avatar */}
                          <div className="w-10 h-10 rounded-full overflow-hidden shrink-0">
                            <Image 
                              src={message.username === "angry_user" ? "/thief.jpg" : 
                                   message.type === "ai" ? "/logo.svg" : "/profile-5.jpg"} 
                              alt={message.type === "ai" ? "AI" : "User"}
                              width={40} 
                              height={40}
                              className={cn(
                                "h-full w-full",
                                message.type === "ai" ? "p-1 bg-primary/10" : "object-cover"
                              )}
                            />
                          </div>
                          
                          {/* Message content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-[#f2f3f5]">{message.username}</span>
                              {message.role && (
                                <span className={cn(
                                  "text-xs px-1 rounded text-white",
                                  message.roleColor || "bg-[#5764f2]"
                                )}>
                                  {message.role}
                                </span>
                              )}
                              <span className="text-xs text-[#989aa2]">
                                {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                              </span>
                            </div>
                            
                            <div className="text-[#e0e1e5] break-words">
                              {message.content}
                            </div>
                          </div>
                        </motion.div>
                      ))}

                      {/* AI typing indicator */}
                      {isAiTyping && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="flex items-start gap-3"
                        >
                          <div className="w-10 h-10 rounded-full overflow-hidden shrink-0 bg-primary/10 flex items-center justify-center">
                            <Image src="/logo.svg" alt="AI" width={20} height={20} className="h-5 w-5" />
                          </div>
                          <div className="mt-4">
                            <div className="flex space-x-1">
                              <motion.div
                                className="h-2 w-2 rounded-full bg-[#b5bac1]"
                                animate={{ y: prefersReducedMotion ? 0 : [0, -5, 0] }}
                                transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1, delay: 0 }}
                              />
                              <motion.div
                                className="h-2 w-2 rounded-full bg-[#b5bac1]"
                                animate={{ y: prefersReducedMotion ? 0 : [0, -5, 0] }}
                                transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1, delay: 0.2 }}
                              />
                              <motion.div
                                className="h-2 w-2 rounded-full bg-[#b5bac1]"
                                animate={{ y: prefersReducedMotion ? 0 : [0, -5, 0] }}
                                transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1, delay: 0.4 }}
                              />
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>

                {/* Message input area */}
                <div className="p-4 pb-6">
                  <div className="bg-[#383a40] rounded-lg">
                    <div className="flex items-center px-4">
                      <button className="p-2 text-[#b5bac1] hover:text-white">
                        <Plus className="h-5 w-5" />
                      </button>
                      
                      <form
                        onSubmit={(e) => {
                          e.preventDefault()
                          handleSendMessage()
                        }}
                        className="flex-1"
                      >
                        <input
                          type="text"
                          placeholder="Message #general"
                          className="w-full py-2.5 px-2 bg-transparent focus:outline-none text-[#e0e1e5]"
                          value={inputValue}
                          onChange={(e) => setInputValue(e.target.value)}
                          disabled={isAiTyping}
                        />
                      </form>
                      
                      <div className="flex items-center text-[#b5bac1]">
                        <button className="p-2 hover:text-white">
                          <Gift className="h-5 w-5" />
                        </button>
                        <button className="p-2 hover:text-white">
                          <Smile className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Members sidebar */}
              <div className="w-[200px] bg-[#2b2d31] border-l border-[#35363a] hidden lg:block">
                <div className="p-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#989aa2]" />
                    <input 
                      type="text" 
                      placeholder="Search" 
                      className="w-full rounded-md bg-[#1e1f22] py-1.5 pl-9 pr-3 text-sm text-[#e0e1e5] placeholder:text-[#989aa2] focus:outline-none"
                    />
                  </div>
                </div>

                <div className="px-2 py-1 overflow-y-auto h-[calc(100%-72px)] scrollbar-thin scrollbar-thumb-[#202225] hover:scrollbar-thumb-[#2a2c31] scrollbar-track-transparent">
                  <h3 className="text-xs font-semibold text-[#989aa2] px-2 py-1">ONLINE — {members.length}</h3>
                  {members.map(member => (
                    <div key={member.id} className="flex items-center gap-2 px-2 py-1.5 rounded hover:bg-[#35363a] cursor-pointer">
                      <div className="relative">
                        <div className="w-8 h-8 rounded-full overflow-hidden">
                          <Image 
                            src={member.image}
                            alt={member.name}
                            width={32}
                            height={32}
                            className={cn(
                              "h-full w-full",
                              member.name === "CommuzaBot" ? "p-1 bg-primary/10" : "object-cover"
                            )}
                          />
                        </div>
                        <div className={cn(
                          "absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-[#2b2d31]",
                          member.status === "online" ? "bg-green-500" : "bg-yellow-500"
                        )}></div>
                      </div>
                      <div className="min-w-0">
                        <div className="flex items-center gap-1.5">
                          <span className="text-sm text-[#f2f3f5] font-medium truncate">{member.name}</span>
                          {member.role && member.role !== "MEMBER" && (
                            <span className={cn(
                              "text-[10px] px-1 py-0.5 rounded text-white",
                              member.role === "BOT" ? "bg-primary" :
                              member.role === "MOD" ? "bg-green-600" :
                              member.role === "ADMIN" ? "bg-red-600" : 
                              "bg-[#5764f2]"
                            )}>
                              {member.role}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
