import { Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

const plans = [
  {
    name: "Free",
    description: "For small communities just getting started.",
    price: "$0",
    features: [
      "1 AI Community Manager",
      "1 platform integration",
      "Basic moderation",
      "Community engagement",
      "100 AI interactions/day",
      "7-day data retention",
    ],
    cta: "Get Started",
    href: "/signup",
    popular: false,
  },
  {
    name: "Pro",
    description: "For growing communities that need more capabilities.",
    price: "$49",
    period: "/month",
    features: [
      "3 AI Community Managers",
      "3 platform integrations",
      "Advanced moderation",
      "Custom personalities",
      "1,000 AI interactions/day",
      "30-day data retention",
      "Analytics dashboard",
      "Priority support",
    ],
    cta: "Start Free Trial",
    href: "/signup?plan=pro",
    popular: true,
  },
  {
    name: "Enterprise",
    description: "For large communities with custom requirements.",
    price: "Custom",
    features: [
      "Unlimited AI Community Managers",
      "All platform integrations",
      "Custom AI training",
      "Advanced analytics",
      "Unlimited AI interactions",
      "90-day data retention",
      "Dedicated support",
      "Custom integrations",
      "SLA guarantees",
    ],
    cta: "Contact Sales",
    href: "/contact",
    popular: false,
  },
]

export function LandingPricing() {
  return (
    <section id="pricing" className="container py-16 md:py-24 lg:py-32 space-y-12">
      <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
        <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">Simple, Transparent Pricing</h2>
        <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
          Choose the plan that's right for your community. All plans include a 14-day free trial.
        </p>
      </div>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {plans.map((plan) => (
          <Card
            key={plan.name}
            className={
              (plan.popular ? "border-primary shadow-md relative " : "border-border ") +
              "flex flex-col h-full"
            }
          >
            {plan.popular && (
              <div className="absolute -top-3 left-0 right-0 flex justify-center">
                <div className="bg-primary text-primary-foreground text-xs font-medium px-3 py-1 rounded-full">
                  Most Popular
                </div>
              </div>
            )}
            <CardHeader>
              <CardTitle className="text-xl">{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className="mt-4 flex items-baseline text-3xl font-bold">
                {plan.price}
                {plan.period && <span className="ml-1 text-sm font-medium text-muted-foreground">{plan.period}</span>}
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-sm">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-primary" />
                    {feature}
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter className="mt-auto">
              <Link href={plan.href} className="w-full">
                <Button variant={plan.popular ? "default" : "outline"} className="w-full">
                  {plan.cta}
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </section>
  )
}
