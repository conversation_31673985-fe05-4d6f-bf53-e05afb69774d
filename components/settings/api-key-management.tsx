"use client"

import type {
  <PERSON><PERSON><PERSON><PERSON>,
  ExpirationPeriod,
  Platform,
} from "@/app/actions/api-keys";
import {
  createApi<PERSON><PERSON>,
  getApi<PERSON><PERSON><PERSON>,
  revoke<PERSON>pi<PERSON><PERSON>,
  updateApi<PERSON>eyName,
} from "@/app/actions/api-keys";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/components/ui/use-toast";
import {
  AlertCircle,
  Check,
  ChevronRight,
  Copy,
  EyeIcon,
  EyeOffIcon,
  Key,
  MessageCircle,
  MessageSquare,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Shield,
  Slack,
} from "lucide-react";
import { useEffect, useState } from "react";

// Helper function to get platform icon
const getPlatformIcon = (platform: Platform) => {
  switch (platform) {
    case "discord":
      return <MessageSquare className="h-5 w-5" />;
    case "telegram":
      return <MessageCircle className="h-5 w-5" />;
    case "slack":
      return <Slack className="h-5 w-5" />;
  }
};

// Helper function to get platform color
const getPlatformColor = (platform: Platform) => {
  switch (platform) {
    case "discord":
      return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300";
    case "telegram":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "slack":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
  }
};

// Helper function to get platform name
const getPlatformName = (platform: Platform) => {
  switch (platform) {
    case "discord":
      return "Discord";
    case "telegram":
      return "Telegram";
    case "slack":
      return "Slack";
  }
};

export function ApiKeyManagement() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newKeyName, setNewKeyName] = useState("");
  const [newKey, setNewKey] = useState<string | null>(null);
  const [visibleKeys, setVisibleKeys] = useState<Record<string, boolean>>({});
  const [isGeneratingKey, setIsGeneratingKey] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(
    null
  );
  const [expirationPeriod, setExpirationPeriod] =
    useState<ExpirationPeriod>("never");

  const fetchApiKeys = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await getApiKeys();

      if (response.error) {
        setError(response.error.message);
        setApiKeys([]);
      } else if (response.data) {
        setApiKeys(response.data);
      } else {
        setApiKeys([]);
      }
    } catch (err) {
      console.error("Failed to fetch API keys:", err);
      setError("Failed to load API keys. Please try again.");
      setApiKeys([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApiKeys();
  }, []);

  const resetDialogState = () => {
    setNewKeyName("");
    setNewKey(null);
    setSelectedPlatform(null);
    setExpirationPeriod("never");
  };

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys((prev) => ({
      ...prev,
      [keyId]: !prev[keyId],
    }));
  };

  // Get existing platforms that already have keys
  const platformsWithKeys = apiKeys
    .filter((key) => !key.revoked)
    .map((key) => key.platform);

  // Available platforms that don't have keys yet
  const availablePlatforms: Platform[] = [
    "discord",
    "telegram",
    "slack",
  ].filter(
    (platform) => !platformsWithKeys.includes(platform as Platform)
  ) as Platform[];

  const generateNewKey = async () => {
    if (!newKeyName.trim()) {
      toast({
        title: "Error",
        description: "Please provide a name for your API key",
        variant: "destructive",
      });
      return;
    }

    if (!selectedPlatform) {
      toast({
        title: "Error",
        description: "Please select a platform for your API key",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingKey(true);

    try {
      const response = await createApiKey({
        name: newKeyName,
        platform: selectedPlatform,
        expirationPeriod,
      });

      if (response.error) {
        toast({
          title: "Error",
          description: response.error.message || "Failed to generate API key",
          variant: "destructive",
        });
        return;
      }

      if (response.data) {
        const generatedKey = response.data;
        setNewKey(generatedKey.key || "");

        // Fetch updated list to ensure we have the latest data
        const updatedKeysResponse = await getApiKeys();
        if (updatedKeysResponse.data) {
          setApiKeys(updatedKeysResponse.data);
        }

        if (generatedKey.id) {
          setVisibleKeys((prev) => ({
            ...prev,
            [generatedKey.id]: true,
          }));
        }

        toast({
          title: "Success",
          description: `New ${getPlatformName(
            selectedPlatform
          )} API key generated successfully`,
        });
      }
    } catch (error) {
      console.error("Error generating API key:", error);
      toast({
        title: "Error",
        description: "Failed to generate API key",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingKey(false);
    }
  };

  const copyToClipboard = (text: string | undefined) => {
    if (!text) {
      toast({
        title: "Error",
        description: "No API key available to copy",
        variant: "destructive",
      });
      return;
    }

    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "API key copied to clipboard",
    });
  };

  const revokeKey = async (keyId: string) => {
    try {
      const response = await revokeApiKey(keyId);

      if (response.error) {
        toast({
          title: "Error",
          description: response.error.message || "Failed to revoke API key",
          variant: "destructive",
        });
        return;
      }

      // Update local state to mark the key as revoked
      setApiKeys((prev) =>
        prev.map((key) => (key.id === keyId ? { ...key, revoked: true } : key))
      );

      toast({
        title: "API Key Revoked",
        description: "The API key has been permanently revoked",
      });
    } catch (error) {
      console.error("Error revoking API key:", error);
      toast({
        title: "Error",
        description: "Failed to revoke API key",
        variant: "destructive",
      });
    }
  };

  const renameKey = async (keyId: string, newName: string) => {
    if (!newName.trim()) {
      toast({
        title: "Error",
        description: "API key name cannot be empty",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await updateApiKeyName(keyId, newName);

      if (response.error) {
        toast({
          title: "Error",
          description: response.error.message || "Failed to rename API key",
          variant: "destructive",
        });
        return;
      }

      // Update local state with the new name
      setApiKeys((prev) =>
        prev.map((key) => (key.id === keyId ? { ...key, name: newName } : key))
      );

      toast({
        title: "API Key Renamed",
        description: "The API key has been renamed successfully",
      });
    } catch (error) {
      console.error("Error renaming API key:", error);
      toast({
        title: "Error",
        description: "Failed to rename API key",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Filter out revoked keys
  const activeKeys = apiKeys.filter((key) => !key.revoked);

  // All platforms in a specific order
  const allPlatforms: Platform[] = ["discord", "telegram", "slack"];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Platform API Keys</CardTitle>
            <CardDescription>
              Manage your API keys for integrating Commuza with different
              platforms.
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={fetchApiKeys}
              disabled={loading}
              title="Refresh API keys"
            >
              <RefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
              <span className="sr-only">Refresh API keys</span>
            </Button>
            {availablePlatforms.length > 0 && (
              <Dialog
                open={isDialogOpen}
                onOpenChange={(open) => {
                  setIsDialogOpen(open);
                  if (!open) {
                    resetDialogState();
                  }
                }}
              >
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    New API Key
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Generate New API Key</DialogTitle>
                    <DialogDescription>
                      Create a new API key for a platform.
                    </DialogDescription>
                  </DialogHeader>

                  {!newKey ? (
                    <div className="space-y-4 py-2">
                      <div className="space-y-2">
                        <Label htmlFor="key-name">API Key Name</Label>
                        <Input
                          id="key-name"
                          placeholder="e.g., Discord Integration"
                          value={newKeyName}
                          onChange={(e) => setNewKeyName(e.target.value)}
                        />
                        <p className="text-xs text-muted-foreground">
                          Give your API key a descriptive name to identify its
                          purpose.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label>Select Platform</Label>
                        <div className="grid grid-cols-1 gap-3">
                          {availablePlatforms.map((platform) => (
                            <Button
                              key={platform}
                              variant={
                                selectedPlatform === platform
                                  ? "default"
                                  : "outline"
                              }
                              className="justify-start h-auto py-3 px-4"
                              onClick={() => setSelectedPlatform(platform)}
                            >
                              <div className="flex items-center">
                                <div
                                  className={`p-2 rounded-md mr-3 ${getPlatformColor(
                                    platform
                                  )}`}
                                >
                                  {getPlatformIcon(platform)}
                                </div>
                                <div className="flex flex-col items-start">
                                  <span className="font-medium">
                                    {getPlatformName(platform)}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    Access all {getPlatformName(platform)} data
                                  </span>
                                </div>
                                {selectedPlatform === platform && (
                                  <ChevronRight className="ml-auto h-5 w-5 text-primary" />
                                )}
                              </div>
                            </Button>
                          ))}
                        </div>
                        {availablePlatforms.length < 3 && (
                          <p className="text-xs text-muted-foreground">
                            Only platforms without existing API keys are
                            available.
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="expiration">Expiration</Label>
                        <Select
                          defaultValue="never"
                          onValueChange={(value) =>
                            setExpirationPeriod(value as ExpirationPeriod)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select expiration period" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="never">Never expires</SelectItem>
                            <SelectItem value="30days">30 days</SelectItem>
                            <SelectItem value="90days">90 days</SelectItem>
                            <SelectItem value="1year">1 year</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                          Choose when this API key should expire. For security,
                          we recommend setting an expiration date.
                        </p>
                      </div>

                      <DialogFooter>
                        <Button
                          onClick={generateNewKey}
                          disabled={
                            isGeneratingKey ||
                            !selectedPlatform ||
                            !newKeyName.trim()
                          }
                        >
                          {isGeneratingKey ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              Generating...
                            </>
                          ) : (
                            "Generate Key"
                          )}
                        </Button>
                      </DialogFooter>
                    </div>
                  ) : (
                    <div className="space-y-4 py-2">
                      <div className="rounded-md bg-green-50 p-4 dark:bg-green-900/20">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <Check
                              className="h-5 w-5 text-green-400"
                              aria-hidden="true"
                            />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-green-800 dark:text-green-400">
                              API Key Generated
                            </h3>
                            <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                              <p>
                                Your new API key has been generated
                                successfully.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Your New API Key</Label>
                        <div className="flex items-center space-x-2">
                          <Input
                            value={newKey}
                            readOnly
                            className="font-mono text-sm"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => copyToClipboard(newKey)}
                          >
                            <Copy className="h-4 w-4" />
                            <span className="sr-only">Copy API key</span>
                          </Button>
                        </div>
                        <p className="text-xs text-destructive font-medium">
                          This key will only be displayed once. Please copy it
                          now and store it securely.
                        </p>
                      </div>

                      <DialogFooter>
                        <Button
                          onClick={() => {
                            setIsDialogOpen(false);
                            resetDialogState();
                          }}
                        >
                          Close
                        </Button>
                      </DialogFooter>
                    </div>
                  )}
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {loading && (
              <div className="flex justify-center items-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">
                  Loading API keys...
                </span>
              </div>
            )}

            {error && !loading && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {!loading &&
            !error &&
            activeKeys.length === 0 &&
            availablePlatforms.length === 0 ? (
              <div className="text-center py-12 border rounded-lg">
                <div className="flex justify-center mb-4">
                  <Key className="h-12 w-12 text-muted-foreground opacity-50" />
                </div>
                <h3 className="text-lg font-medium mb-2">No API Keys</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any platform API keys yet. Generate one to get
                  started.
                </p>
                <Button onClick={() => setIsDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Generate API Key
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {allPlatforms.map((platform) => {
                  const platformKey = activeKeys.find(
                    (key) => key.platform === platform
                  );
                  const isAvailable = availablePlatforms.includes(platform);

                  return (
                    <div
                      key={platform}
                      className="rounded-lg border overflow-hidden"
                    >
                      <div
                        className={`p-4 flex items-center justify-between ${
                          platformKey
                            ? getPlatformColor(platform)
                            : "bg-muted/20"
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className={`p-1.5 rounded-md ${
                              platformKey
                                ? "bg-white/20 dark:bg-black/20"
                                : "bg-muted"
                            }`}
                          >
                            {getPlatformIcon(platform)}
                          </div>
                          <div>
                            <h3 className="font-medium">
                              {getPlatformName(platform)}
                            </h3>
                            <p className="text-xs opacity-80">
                              {platformKey
                                ? "API key active"
                                : "No API key generated"}
                            </p>
                          </div>
                        </div>
                        {platformKey ? (
                          <Badge
                            variant="outline"
                            className="text-xs bg-white/30 dark:bg-black/30"
                          >
                            Active
                          </Badge>
                        ) : isAvailable ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white/30 dark:bg-black/30 hover:bg-white/50 dark:hover:bg-black/50"
                            onClick={() => {
                              setSelectedPlatform(platform);
                              setIsDialogOpen(true);
                            }}
                          >
                            <Plus className="h-3.5 w-3.5 mr-1" />
                            Generate Key
                          </Button>
                        ) : (
                          <Badge
                            variant="outline"
                            className="text-xs bg-white/30 dark:bg-black/30"
                          >
                            Revoked
                          </Badge>
                        )}
                      </div>

                      {platformKey && (
                        <div className="p-4 space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{platformKey.name}</h4>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">More options</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => {
                                    const newName = prompt(
                                      "Enter new name for API key",
                                      platformKey.name
                                    );
                                    if (newName && newName.trim() !== "") {
                                      renameKey(platformKey.id, newName);
                                    }
                                  }}
                                >
                                  Rename
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    copyToClipboard(platformKey.key_prefix)
                                  }
                                >
                                  Copy to clipboard
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() => {
                                    if (
                                      confirm(
                                        "Are you sure you want to revoke this API key? This action cannot be undone."
                                      )
                                    ) {
                                      revokeKey(platformKey.id);
                                    }
                                  }}
                                >
                                  Revoke key
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>

                          <div className="flex items-center space-x-2">
                            <div className="relative flex-1">
                              <Input
                                value={
                                  visibleKeys[platformKey.id]
                                    ? platformKey.key || platformKey.key_prefix
                                    : "•".repeat(40)
                                }
                                readOnly
                                className="pr-10 font-mono text-sm"
                              />
                              <Button
                                variant="ghost"
                                size="icon"
                                className="absolute right-0 top-0 h-full"
                                onClick={() =>
                                  toggleKeyVisibility(platformKey.id)
                                }
                              >
                                {visibleKeys[platformKey.id] ? (
                                  <EyeOffIcon className="h-4 w-4" />
                                ) : (
                                  <EyeIcon className="h-4 w-4" />
                                )}
                                <span className="sr-only">
                                  {visibleKeys[platformKey.id]
                                    ? "Hide"
                                    : "Show"}{" "}
                                  API key
                                </span>
                              </Button>
                            </div>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() =>
                                copyToClipboard(platformKey.key_prefix)
                              }
                            >
                              <Copy className="h-4 w-4" />
                              <span className="sr-only">Copy API key</span>
                            </Button>
                          </div>

                          <div className="flex flex-col space-y-1 text-xs text-muted-foreground">
                            <div>
                              Created: {formatDate(platformKey.createdAt)}
                            </div>
                            {platformKey.lastUsed && (
                              <div>
                                Last used: {formatDate(platformKey.lastUsed)}
                              </div>
                            )}
                            {platformKey.expiresAt && (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <span>
                                        Expires:{" "}
                                        {formatDate(platformKey.expiresAt)}
                                      </span>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>
                                      This key will expire on{" "}
                                      {new Date(
                                        platformKey.expiresAt
                                      ).toLocaleString()}
                                    </p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          </div>
                        </div>
                      )}

                      {!platformKey && (
                        <div className="p-6 flex flex-col items-center justify-center text-center">
                          {isAvailable ? (
                            <>
                              <Shield className="h-8 w-8 text-muted-foreground mb-2 opacity-50" />
                              <h4 className="text-sm font-medium mb-1">
                                No {getPlatformName(platform)} API Key
                              </h4>
                              <p className="text-xs text-muted-foreground mb-4">
                                Generate an API key to integrate with{" "}
                                {getPlatformName(platform)}.
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedPlatform(platform);
                                  setIsDialogOpen(true);
                                }}
                              >
                                <Plus className="h-3.5 w-3.5 mr-1" />
                                Generate Key
                              </Button>
                            </>
                          ) : (
                            <>
                              <Shield className="h-8 w-8 text-muted-foreground mb-2 opacity-50" />
                              <h4 className="text-sm font-medium mb-1">
                                {getPlatformName(platform)} API Key Revoked
                              </h4>
                              <p className="text-xs text-muted-foreground mb-4">
                                This API key has been revoked. Generate a new
                                one to restore access.
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedPlatform(platform);
                                  setIsDialogOpen(true);
                                }}
                              >
                                <Plus className="h-3.5 w-3.5 mr-1" />
                                Generate New Key
                              </Button>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Platform API Key Security</CardTitle>
          <CardDescription>
            Best practices for managing your platform API keys.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Platform-Specific Security</h4>
              <div className="space-y-4 mt-2">
                <div className="flex items-start space-x-3">
                  <div className="mt-0.5">
                    <Badge className={`${getPlatformColor("discord")}`}>
                      <MessageSquare className="h-3 w-3 mr-1" />
                      Discord
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Discord API keys provide access to all your Discord channels
                    and data. Use these keys with Discord bots and integrations.
                    Never share these keys in public Discord channels.
                  </p>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="mt-0.5">
                    <Badge className={`${getPlatformColor("telegram")}`}>
                      <MessageCircle className="h-3 w-3 mr-1" />
                      Telegram
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Telegram API keys provide access to all your Telegram groups
                    and channels. Use these keys with Telegram bots. Store these
                    keys securely and never expose them in client-side code.
                  </p>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="mt-0.5">
                    <Badge className={`${getPlatformColor("slack")}`}>
                      <Slack className="h-3 w-3 mr-1" />
                      Slack
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Slack API keys provide access to all your Slack workspaces
                    and channels. Use these keys with Slack apps and
                    integrations. Use environment variables to store these keys
                    in your applications.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Security Best Practices</h4>
              <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                <li>
                  Store API keys securely using environment variables in your
                  applications.
                </li>
                <li>
                  Never expose API keys in client-side code or public
                  repositories.
                </li>
                <li>
                  Set appropriate expiration dates for keys that don't need
                  permanent access.
                </li>
                <li>
                  Revoke keys immediately if they are compromised or no longer
                  needed.
                </li>
                <li>Regularly rotate your API keys for enhanced security.</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
