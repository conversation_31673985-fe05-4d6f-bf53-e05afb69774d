import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface LogoProps {
  className?: string
  href?: string
  showText?: boolean
  size?: "sm" | "md" | "lg"
}

export function Logo({ className, href = "/", showText = true, size = "md" }: LogoProps) {
  const sizes = {
    sm: { logo: 24, height: 24 },
    md: { logo: 32, height: 32 },
    lg: { logo: 48, height: 48 },
  }

  const { logo, height } = sizes[size]

  return (
    <Link href={href} className={cn("flex items-center gap-2", className)}>
      <div className="relative shrink-0" style={{ height, width: logo }}>
        <Image src="/logo.svg" alt="Commuza Logo" fill priority className="object-contain" />
      </div>
      {showText && (
        <span
          className={cn("font-bold", {
            "text-lg": size === "sm",
            "text-xl": size === "md",
            "text-2xl": size === "lg",
          })}
        >
          Commuza
        </span>
      )}
    </Link>
  )
}
