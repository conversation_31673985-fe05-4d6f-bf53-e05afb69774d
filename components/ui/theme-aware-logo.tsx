"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface ThemeAwareLogoProps {
  className?: string
  href?: string
  showText?: boolean
  size?: "sm" | "md" | "lg"
}

export function ThemeAwareLogo({ className, href = "/", showText = true, size = "md" }: ThemeAwareLogoProps) {
  const { resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted to avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  const sizes = {
    sm: { logo: 24, height: 24 },
    md: { logo: 32, height: 32 },
    lg: { logo: 48, height: 48 },
  }

  const { logo, height } = sizes[size]

  // Use a placeholder during SSR to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="relative shrink-0" style={{ height, width: logo }}>
          <div className="h-full w-full rounded-full bg-primary/20 animate-pulse" />
        </div>
        {showText && (
          <div
            className={cn("bg-foreground/20 animate-pulse rounded", {
              "h-4 w-20": size === "sm",
              "h-5 w-24": size === "md",
              "h-6 w-32": size === "lg",
            })}
          />
        )}
      </div>
    )
  }

  return (
    <Link href={href} className={cn("flex items-center gap-2", className)}>
      <div className="relative shrink-0" style={{ height, width: logo }}>
        <Image src="/logo.svg" alt="Commuza Logo" fill priority className="object-contain" />
      </div>
      {showText && (
        <span
          className={cn("font-bold", {
            "text-lg": size === "sm",
            "text-xl": size === "md",
            "text-2xl": size === "lg",
          })}
        >
          Commuza
        </span>
      )}
    </Link>
  )
}
