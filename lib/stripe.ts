import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY environment variable is missing. Please set it in your .env.local file.');
}
import { loadStripe } from '@stripe/stripe-js';
import { SupabaseClient } from '@supabase/supabase-js';

// Initialize Stripe with your secret key
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-03-31.basil',
});

// Stripe Promise for client-side usage
export const getStripe = async () => {
  const stripePublicKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string;
  return loadStripe(stripePublicKey);
};

export type PricingPlan = {
  id: string;
  name: string;
  description: string;
  priceMonthly: number;
  priceYearly: number;
  features: string[];
  popular: boolean;
  recommended: boolean;
  stripeMonthlyPriceId: string;
  stripeYearlyPriceId: string;
};

// Define pricing plans
export const pricingPlans: Record<string, PricingPlan> = {
  free: {
    id: 'free',
    name: 'Free',
    description: 'For small communities just getting started.',
    priceMonthly: 0,
    priceYearly: 0,
    stripeMonthlyPriceId: '',
    stripeYearlyPriceId: '',
    features: [
      '1 AI Community Manager',
      '1 platform integration',
      'Basic moderation',
      'Community engagement',
      '100 AI interactions/day',
      '7-day data retention'
    ],
    popular: false,
    recommended: false
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    description: 'For growing communities that need more capabilities.',
    priceMonthly: 49,
    priceYearly: 490,
    stripeMonthlyPriceId: 'price_1OqXyZCiJgbx7WaUJv6frvZu',
    stripeYearlyPriceId: 'price_1OqXyZCiJgbx7WaUYugR9uX1',
    features: [
      '3 AI Community Managers',
      '3 platform integrations',
      'Advanced moderation',
      'Custom personalities',
      '1,000 AI interactions/day',
      '30-day data retention',
      'Analytics dashboard',
      'Priority support'
    ],
    popular: true,
    recommended: false
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large communities with custom requirements.',
    priceMonthly: 999,
    priceYearly: 9990,
    stripeMonthlyPriceId: 'price_1OqXzQCiJgbx7WaUCWxc5APx',
    stripeYearlyPriceId: 'price_1OqXzQCiJgbx7WaUmkrNW9jH',
    features: [
      'Unlimited AI Community Managers',
      'All platform integrations',
      'Custom AI training',
      'Advanced analytics',
      'Unlimited AI interactions',
      '90-day data retention',
      'Dedicated support',
      'Custom integrations',
      'SLA guarantees'
    ],
    popular: false,
    recommended: false
  }
};

// Get customer by user ID
export async function getCustomerByUserId(supabase: SupabaseClient , userId: string) {
  const { data, error } = await supabase
    .from('billing_customers')
    .select('stripe_customer_id')
    .eq('user_id', userId)
    .single();

  if (error || !data) {
    return null;
  }

  return data.stripe_customer_id;
}

// Create or retrieve a Stripe customer
export async function createOrRetrieveCustomer(
  supabase: SupabaseClient,
  userId: string,
  email: string,
  name?: string
) {
  // Check if customer exists
  const { data: existingCustomer } = await supabase
    .from('billing_customers')
    .select('stripe_customer_id')
    .eq('user_id', userId)
    .single();

  if (existingCustomer?.stripe_customer_id) {
    // Return existing customer
    return existingCustomer.stripe_customer_id;
  }

  // Create new customer in Stripe
  const customer = await stripe.customers.create({
    email,
    name,
    metadata: {
      userId,
    },
  });

  // Store customer in database
  await supabase.from('billing_customers').insert([
    {
      user_id: userId,
      stripe_customer_id: customer.id,
    },
  ]);

  return customer.id;
}

// Get subscription by customer ID
export async function getSubscriptionByCustomerId(customerId: string) {
  const subscriptions = await stripe.subscriptions.list({
    customer: customerId,
    status: 'all',
    expand: ['data.default_payment_method', 'data.items.data.price.product'],
  });

  return subscriptions.data[0];
}

// Create checkout session
export async function createCheckoutSession({
  customerId,
  priceId,
  returnUrl,
}: {
  customerId: string;
  priceId: string;
  returnUrl: string;
}) {
  const checkoutSession = await stripe.checkout.sessions.create({
    customer: customerId,
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${returnUrl}?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: returnUrl,
  });

  return checkoutSession;
}

// Create a portal session
export async function createPortalSession({
  customerId,
  returnUrl,
}: {
  customerId: string;
  returnUrl: string;
}) {
  const portalSession = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  });

  return portalSession;
}

// Cancel subscription
export async function cancelSubscription(subscriptionId: string) {
  return await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: true,
  });
}

// Reactivate subscription
export async function reactivateSubscription(subscriptionId: string) {
  return await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: false,
  });
}

// Update subscription
export async function updateSubscription({
  subscriptionId,
  priceId,
}: {
  subscriptionId: string;
  priceId: string;
}) {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  const itemId = subscription.items.data[0].id;

  return await stripe.subscriptions.update(subscriptionId, {
    items: [
      {
        id: itemId,
        price: priceId,
      },
    ],
  });
}