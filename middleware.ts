import { updateSession } from "@/lib/supabase/middleware";
import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  let { user, response } = await updateSession(request);
  if (request.nextUrl.pathname === "/api/verify-key") {
    response = NextResponse.next();
  } else if (
    !user &&
    request.nextUrl.pathname !== "/login" &&
    request.nextUrl.pathname !== "/" &&
    request.nextUrl.pathname !== "/signup"
  ) {
    // If the user is not authenticated, redirect to the login page
    response = NextResponse.redirect(new URL("/login", request.url));
  } else if (user && request.nextUrl.pathname === "/login") {
    response = NextResponse.redirect(new URL("/dashboard", request.url));
  } else {
    response = NextResponse.next();
  }
  return response;
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
