{"name": "commuza-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@marsidev/react-turnstile": "^1.1.0", "@mdx-js/react": "^3.1.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stripe/stripe-js": "^7.2.0", "@supabase/auth-helpers-nextjs": "latest", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "latest", "@vercel/analytics": "^1.5.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "gray-matter": "^4.0.3", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "motion": "^12.9.2", "next-mdx-remote": "^5.0.0", "next-themes": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-icons": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "stripe": "^18.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@mdx-js/loader": "^3.1.0", "@next/mdx": "^15.3.1", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "next": "^15.3.1", "postcss": "^8", "remark": "^15.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx-frontmatter": "^5.1.0", "tailwindcss": "^3.4.17", "typescript": "^5", "unified": "^11.0.5", "wrangler": "^4.16.1"}}