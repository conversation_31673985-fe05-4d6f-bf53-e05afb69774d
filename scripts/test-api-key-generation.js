// Test script for API key generation and verification
const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !SUPABASE_SERVICE_KEY) {
  console.error('Missing required environment variables. Please set:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- NEXT_PUBLIC_SUPABASE_ANON_KEY');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase clients
const supabase = createClient(SUPABASE_URL, SUP<PERSON>ASE_ANON_KEY);
const adminSupabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testApiKeyGeneration() {
  console.log('=== Testing API Key Generation ===');
  
  // 1. Generate a test API key
  const platform = 'discord';
  const keyPrefix = `cmz_${platform.substring(0, 2)}_${crypto.randomBytes(4).toString('hex')}`;
  const keySecret = crypto.randomBytes(24).toString('hex');
  const fullApiKey = `${keyPrefix}_${keySecret}`;
  
  // Hash the key for storage
  const keyHash = crypto.createHash('sha256').update(fullApiKey).digest('hex');
  
  console.log(`Generated key: ${fullApiKey}`);
  console.log(`Key prefix: ${keyPrefix}`);
  console.log(`Key hash: ${keyHash}`);
  
  // 2. Insert the key into the database
  const { data: insertedKey, error: insertError } = await adminSupabase
    .from('api_keys')
    .insert({
      organization_id: '00000000-0000-0000-0000-000000000000', // Use a test organization ID
      name: 'Test API Key',
      key_prefix: keyPrefix,
      key_hash: keyHash,
      platform,
      scopes: [`platform:${platform}`, 'read'],
      created_by: '00000000-0000-0000-0000-000000000000', // Use a test user ID
    })
    .select()
    .single();
  
  if (insertError) {
    console.error('Error inserting API key:', insertError);
    return;
  }
  
  console.log('Successfully inserted API key:', insertedKey.id);
  
  // 3. Verify the key can be retrieved
  const { data: retrievedKey, error: retrieveError } = await adminSupabase
    .from('api_keys')
    .select('*')
    .eq('key_prefix', keyPrefix)
    .eq('key_hash', keyHash)
    .single();
  
  if (retrieveError) {
    console.error('Error retrieving API key:', retrieveError);
    return;
  }
  
  console.log('Successfully retrieved API key:', retrievedKey.id);
  console.log('Key verification successful!');
  
  // 4. Clean up - delete the test key
  const { error: deleteError } = await adminSupabase
    .from('api_keys')
    .delete()
    .eq('id', insertedKey.id);
  
  if (deleteError) {
    console.error('Error deleting test API key:', deleteError);
    return;
  }
  
  console.log('Test API key deleted successfully');
}

// Run the test
testApiKeyGeneration()
  .catch(err => {
    console.error('Test failed with error:', err);
  })
  .finally(() => {
    process.exit(0);
  });
