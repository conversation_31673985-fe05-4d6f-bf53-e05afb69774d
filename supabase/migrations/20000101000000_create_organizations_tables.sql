CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create private schema for security definer functions
CREATE SCHEMA IF NOT EXISTS private;

-- Organizations table (for team-based access)
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a trigger function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to organizations table
CREATE TRIGGER set_organizations_updated_at
BEFORE UPDATE ON organizations
FOR EACH ROW EXECUTE FUNCTION set_updated_at();

-- Organization members with roles
CREATE TABLE IF NOT EXISTS organization_members (
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- Assumes auth.users table exists
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (organization_id, user_id)
);

ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;

-- Add trigger to organization_members table
CREATE TRIGGER set_organization_members_updated_at
BEFORE UPDATE ON organization_members
FOR EACH ROW EXECUTE FUNCTION set_updated_at();

-- Helper function to get user role in an organization
-- This function uses SECURITY DEFINER to bypass RLS on organization_members when called from a policy
CREATE OR REPLACE FUNCTION private.get_user_role_in_organization(org_id_param UUID, user_id_param UUID)
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
-- IMPORTANT: Set a secure search_path for SECURITY DEFINER functions
SET search_path = public
AS $$
  SELECT role
  FROM public.organization_members
  WHERE organization_id = org_id_param AND user_id = user_id_param;
$$;

-- RLS Policies for organizations
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Users can read organizations they are members of
DROP POLICY IF EXISTS "Users can read organizations they belong to" ON organizations;
CREATE POLICY "Users can read organizations they belong to" ON organizations
  FOR SELECT USING (
    private.get_user_role_in_organization(id, auth.uid()) IS NOT NULL
  );

-- Only organization owners and admins can update organization details
DROP POLICY IF EXISTS "Only owners and admins can update organizations" ON organizations;
CREATE POLICY "Only owners and admins can update organizations" ON organizations
  FOR UPDATE USING (
    private.get_user_role_in_organization(id, auth.uid()) IN ('owner', 'admin')
  ) WITH CHECK (
    private.get_user_role_in_organization(id, auth.uid()) IN ('owner', 'admin')
  );


-- Allow owners to delete their organizations
DROP POLICY IF EXISTS "Owners can delete their organizations" ON organizations;
CREATE POLICY "Owners can delete their organizations" ON organizations
  FOR DELETE USING (
    private.get_user_role_in_organization(id, auth.uid()) = 'owner'
  );

-- RLS Policies for organization_members
-- Users can read organization members list for orgs they belong to
DROP POLICY IF EXISTS "Users can read organization members" ON organization_members;
CREATE POLICY "Users can read organization members" ON organization_members
  FOR SELECT USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IS NOT NULL
  );

-- Only owners and admins can manage members
DROP POLICY IF EXISTS "Only owners and admins can manage members" ON organization_members;
CREATE POLICY "Only owners and admins can manage members" ON organization_members
  FOR ALL USING ( -- Applies to INSERT, UPDATE, DELETE
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  ) WITH CHECK (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  );

-- Function to create an organization and assign the calling user as owner.
-- This function should be called by the newly signed-up user via RPC.
-- The SECURITY DEFINER clause allows it to bypass RLS for the inserts.
CREATE OR REPLACE FUNCTION public.create_organization_and_set_owner(
  org_name TEXT,
  org_slug TEXT
  -- The owner_user_id is implicitly auth.uid() of the authenticated user calling this function.
)
RETURNS UUID -- Returns the new organization_id
LANGUAGE plpgsql
SECURITY DEFINER
-- IMPORTANT: Set a secure search_path for SECURITY DEFINER functions
-- This helps prevent potential attack vectors.
SET search_path = public, pg_temp
AS $$
DECLARE
  new_org_id UUID;
  caller_user_id UUID := auth.uid(); -- Get the UID of the user calling this function.
BEGIN
  -- Ensure the function is called by an authenticated user.
  IF caller_user_id IS NULL THEN
    RAISE EXCEPTION 'User ID not found. This function must be called by an authenticated user.';
  END IF;

  -- Insert the organization.
  -- Explicitly list columns for clarity and security.
  INSERT INTO public.organizations (name, slug, created_at, updated_at)
  VALUES (org_name, org_slug, NOW(), NOW())
  RETURNING id INTO new_org_id;

  -- Assign the caller as the owner in organization_members.
  INSERT INTO public.organization_members (organization_id, user_id, role, created_at, updated_at)
  VALUES (new_org_id, caller_user_id, 'owner', NOW(), NOW());

  RETURN new_org_id;
END;
$$;

-- Grant execute permission on this function to the 'authenticated' role.
-- This allows any authenticated user to call this function.
GRANT EXECUTE ON FUNCTION public.create_organization_and_set_owner(TEXT, TEXT) TO authenticated;

-- Ensure the set_updated_at function and triggers are correctly defined as in your existing migration.
