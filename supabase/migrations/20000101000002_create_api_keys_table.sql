CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- API Keys table for organization-level authentication
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  key_prefix VARCHAR(20) NOT NULL,
  key_hash TEXT NOT NULL,
  scopes TEXT[] NOT NULL DEFAULT array['read'],
  created_by UUID NOT NULL REFERENCES auth.users(id), -- Assumes auth.users table exists
  expires_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance optimization
CREATE INDEX api_keys_organization_id_idx ON api_keys(organization_id);
CREATE INDEX api_keys_key_prefix_idx ON api_keys(key_prefix);
CREATE INDEX api_keys_expires_at_idx ON api_keys(expires_at);
CREATE INDEX api_keys_created_by_idx ON api_keys(created_by);

-- Comment for security best practices
COMMENT ON TABLE api_keys IS 'API keys for organization access. Only key prefixes and hashes are stored for security.';

-- RLS Policies for API Keys table
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Users can read API keys for organizations they are admins/owners of
DROP POLICY IF EXISTS "Users can read API keys for their organizations" ON api_keys;
CREATE POLICY "Users can read API keys for their organizations" ON api_keys
  FOR SELECT USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  );

-- Only organization owners and admins can create API keys
DROP POLICY IF EXISTS "Only owners and admins can create API keys" ON api_keys;
CREATE POLICY "Only owners and admins can create API keys" ON api_keys
  FOR INSERT WITH CHECK (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
    AND created_by = auth.uid() -- Ensure the creator is the authenticated user
  );

-- Only organization owners and admins can update API keys
DROP POLICY IF EXISTS "Only owners and admins can update API keys" ON api_keys;
CREATE POLICY "Only owners and admins can update API keys" ON api_keys
  FOR UPDATE USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  ) WITH CHECK (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  );

-- Only organization owners and admins can delete API keys
DROP POLICY IF EXISTS "Only owners and admins can delete API keys" ON api_keys;
CREATE POLICY "Only owners and admins can delete API keys" ON api_keys
  FOR DELETE USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  );

-- Function to update last_used_at timestamp and updated_at
CREATE OR REPLACE FUNCTION update_api_key_usage_timestamps()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_used_at = NOW();
  NEW.updated_at = NOW(); -- Ensure updated_at is also set
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update last_used_at AND updated_at when the key is verified (last_used_at changes)
CREATE TRIGGER update_api_key_usage_trigger
BEFORE UPDATE ON api_keys
FOR EACH ROW
WHEN (OLD.last_used_at IS DISTINCT FROM NEW.last_used_at) -- Only if last_used_at is actually changing
EXECUTE FUNCTION update_api_key_usage_timestamps();

-- General trigger for updated_at on other updates (if not covered by specific trigger)
-- Assuming set_updated_at() was created in the previous organizations migration.
CREATE TRIGGER set_api_keys_updated_at
BEFORE UPDATE ON api_keys
FOR EACH ROW
WHEN (OLD.last_used_at IS NOT DISTINCT FROM NEW.last_used_at) -- Only if the specific trigger didn't run
EXECUTE FUNCTION set_updated_at(); -- Assumes set_updated_at() exists from previous migration
