CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Platform Agents Integration Schema

-- Reference table for supported platforms
CREATE TABLE IF NOT EXISTS integration_platforms (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  settings_schema JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default supported platforms
INSERT INTO integration_platforms (id, name, description)
VALUES 
  ('discord', 'Discord', 'Discord server moderation and engagement'),
  ('telegram', 'Telegram', 'Telegram group and channel management'),
  ('slack', 'Slack', 'Slack workspace management')
ON CONFLICT (id) DO NOTHING;

-- Table for platform integrations/agents
CREATE TABLE IF NOT EXISTS agents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  platform_id TEXT NOT NULL REFERENCES integration_platforms(id),
  external_id TEXT NOT NULL, -- platform-specific ID (guild_id for Discord, chat_id for Telegram, etc.)
  name TEXT, -- friendly name (guild_name, channel name, etc.)
  api_key_id UUID NOT NULL REFERENCES api_keys(id) ON DELETE CASCADE, -- Assumes api_keys table exists
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE, -- Assumes organizations table exists
  created_by UUID REFERENCES auth.users(id) DEFAULT auth.uid(), -- Added created_by column
  activated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE,
  settings JSONB DEFAULT '{"moderation_enabled": true}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(platform_id, external_id)
);

-- Indexes for performance optimization
CREATE INDEX agents_api_key_id_idx ON agents(api_key_id);
CREATE INDEX agents_organization_id_idx ON agents(organization_id);
CREATE INDEX agents_platform_id_idx ON agents(platform_id);
CREATE INDEX agents_external_id_idx ON agents(external_id);
CREATE INDEX agents_last_active_at_idx ON agents(last_active_at);

-- Usage statistics for platform agents
CREATE TABLE IF NOT EXISTS agent_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  channel_id TEXT, -- platform sub-channel where applicable (Discord channel, Telegram thread, etc.)
  message_id TEXT,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX agent_events_agent_id_idx ON agent_events(agent_id);
CREATE INDEX agent_events_created_at_idx ON agent_events(created_at);
CREATE INDEX agent_events_event_type_idx ON agent_events(event_type);

-- Comments for documentation
COMMENT ON TABLE integration_platforms IS 'Supported integration platforms (Discord, Telegram, Slack, etc.)';
COMMENT ON TABLE agents IS 'Platform-specific integrations using Commuza API keys for moderation and engagement';
COMMENT ON TABLE agent_events IS 'Usage events from platform integrations';

-- RLS Policies for agents table
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;

-- Users can read agents for their organizations
DROP POLICY IF EXISTS "Users can read agents for their organizations" ON agents;
CREATE POLICY "Users can read agents for their organizations" ON agents
  FOR SELECT USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IS NOT NULL
  );

-- Only organization owners and admins can create agents
DROP POLICY IF EXISTS "Only owners and admins can create agents" ON agents;
CREATE POLICY "Only owners and admins can create agents" ON agents
  FOR INSERT WITH CHECK (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
    AND created_by = auth.uid() -- Ensure the creator is the authenticated user
  );

-- Only organization owners and admins can update agents
DROP POLICY IF EXISTS "Only owners and admins can update agents" ON agents;
CREATE POLICY "Only owners and admins can update agents" ON agents
  FOR UPDATE USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  ) WITH CHECK (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  );

-- Only organization owners and admins can delete agents
DROP POLICY IF EXISTS "Only owners and admins can delete agents" ON agents;
CREATE POLICY "Only owners and admins can delete agents" ON agents
  FOR DELETE USING (
    private.get_user_role_in_organization(organization_id, auth.uid()) IN ('owner', 'admin')
  );

-- RLS Policies for agent_events table
ALTER TABLE agent_events ENABLE ROW LEVEL SECURITY;

-- Users can read agent events for their organizations
DROP POLICY IF EXISTS "Users can read agent events for their organizations" ON agent_events;
CREATE POLICY "Users can read agent events for their organizations" ON agent_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1
      FROM agents a
      WHERE a.id = agent_events.agent_id AND
            private.get_user_role_in_organization(a.organization_id, auth.uid()) IS NOT NULL
    )
  );

-- API service can insert events (will be handled via service role)
CREATE POLICY "API service can insert agent events" ON agent_events
  FOR INSERT WITH CHECK (true); -- Assuming workers use a service role

-- Function to update the last_active_at timestamp on agents
CREATE OR REPLACE FUNCTION update_agent_last_active()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE agents
  SET last_active_at = NOW(), updated_at = NOW() -- Also update updated_at
  WHERE id = NEW.agent_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update last_active_at when a new event is recorded
CREATE TRIGGER update_agent_last_active_trigger
AFTER INSERT ON agent_events
FOR EACH ROW
EXECUTE FUNCTION update_agent_last_active();

-- General trigger for updated_at on agents table
-- Assumes set_updated_at() function was created in a previous migration (e.g., for organizations)
CREATE TRIGGER set_agents_updated_at
BEFORE UPDATE ON agents
FOR EACH ROW
EXECUTE FUNCTION set_updated_at(); -- Assumes set_updated_at() exists

-- Function to verify an API key belongs to the organization and is active for a specific platform
CREATE OR REPLACE FUNCTION is_valid_agent_api_key(p_api_key_id UUID, p_platform_id TEXT, p_external_id TEXT) 
RETURNS BOOLEAN AS $$
DECLARE
  v_is_valid BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 
    FROM api_keys ag_k -- Renamed to avoid conflict with agents table alias further up
    JOIN agents ag ON ag_k.id = ag.api_key_id -- Renamed to avoid conflict
    WHERE ag_k.id = p_api_key_id
    AND ag.platform_id = p_platform_id
    AND ag.external_id = p_external_id
    AND (ag_k.expires_at IS NULL OR ag_k.expires_at > NOW())
  ) INTO v_is_valid;
  
  RETURN v_is_valid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
