-- Migration to remove the create_api_key RPC function and ensure api_keys table has all necessary columns

-- First, drop the RPC function if it exists
DROP FUNCTION IF EXISTS create_api_key(UUID, TEXT, TEXT[], TIMES<PERSON>MP WITH TIME ZONE);

-- Add platform column to api_keys table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'api_keys' 
        AND column_name = 'platform'
    ) THEN
        ALTER TABLE api_keys ADD COLUMN platform TEXT;
    END IF;
END $$;

-- Create index on platform column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'api_keys'
        AND indexname = 'api_keys_platform_idx'
    ) THEN
        CREATE INDEX api_keys_platform_idx ON api_keys(platform);
    END IF;
END $$;

-- Add comment to explain the change
COMMENT ON TABLE api_keys IS 'API keys for organization access. Keys are now created directly via INSERT instead of RPC function.';
