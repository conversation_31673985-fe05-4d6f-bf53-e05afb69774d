-- Add revoked_at column to api_keys table for soft deletion
-- This allows us to track when API keys were revoked without losing the data

-- Add the revoked_at column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'api_keys' 
        AND column_name = 'revoked_at'
    ) THEN
        ALTER TABLE api_keys ADD COLUMN revoked_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Create index on revoked_at column for performance
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'api_keys'
        AND indexname = 'api_keys_revoked_at_idx'
    ) THEN
        CREATE INDEX api_keys_revoked_at_idx ON api_keys(revoked_at);
    END IF;
END $$;

-- Update the comment to reflect the new column
COMMENT ON TABLE api_keys IS 'API keys for organization access. Keys can be soft-deleted using revoked_at timestamp.';

-- Update the function that validates API keys to exclude revoked keys
CREATE OR REPLACE FUNCTION is_valid_agent_api_key(p_api_key_id UUID, p_platform_id TEXT, p_external_id TEXT) 
RETURNS BOOLEAN AS $$
DECLARE
  v_is_valid BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 
    FROM api_keys ag_k
    JOIN agents ag ON ag_k.id = ag.api_key_id
    WHERE ag_k.id = p_api_key_id
    AND ag.platform_id = p_platform_id
    AND ag.external_id = p_external_id
    AND (ag_k.expires_at IS NULL OR ag_k.expires_at > NOW())
    AND ag_k.revoked_at IS NULL -- Exclude revoked keys
  ) INTO v_is_valid;
  
  RETURN v_is_valid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
