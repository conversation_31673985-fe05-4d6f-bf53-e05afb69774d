-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create messages table
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  channel_id TEXT NULL, -- Platform specific channel ID (e.g., Discord channel ID)
  user_id TEXT NULL, -- Platform specific user ID
  content TEXT,
  direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
  "timestamp" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  discord_message_id TEXT NULL UNIQUE, -- Discord specific message ID
  discord_guild_id TEXT NULL, -- Discord specific guild ID
  discord_channel_id TEXT NULL, -- Discord specific channel ID (duplicate of channel_id but more specific)
  discord_user_id TEXT NULL, -- Discord specific user ID (duplicate of user_id but more specific)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX messages_org_id_idx ON messages(org_id);
CREATE INDEX messages_agent_id_idx ON messages(agent_id);
CREATE INDEX messages_timestamp_idx ON messages("timestamp");
CREATE INDEX messages_discord_message_id_idx ON messages(discord_message_id);

-- Enable RLS
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can read messages for their organizations" ON messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = messages.org_id
      AND user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can insert messages" ON messages
  FOR INSERT WITH CHECK (true); -- Assuming workers use a service role
