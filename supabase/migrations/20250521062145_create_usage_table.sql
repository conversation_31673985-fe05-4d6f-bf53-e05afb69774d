-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create organization_usage table
CREATE TABLE organization_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  month DATE NOT NULL, -- First day of the month
  discord_message_count INTEGER DEFAULT 0,
  discord_flagged_count INTEGER DEFAULT 0,
  -- Add other metrics as needed in the future e.g. telegram_message_count
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(org_id, month)
);

-- <PERSON>reate indexes
CREATE INDEX organization_usage_org_id_month_idx ON organization_usage(org_id, month);

-- Enable RLS
ALTER TABLE organization_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can read usage for their organizations" ON organization_usage
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = organization_usage.org_id
      AND user_id = auth.uid()
    )
  );

-- Drop the combined policy if it exists to avoid conflict
DROP POLICY IF EXISTS "Service role can insert/update usage" ON organization_usage;

-- Policy for INSERT
CREATE POLICY "Service role can insert usage" ON organization_usage
  FOR INSERT
  WITH CHECK (true); -- Assuming aggregation jobs use a service role

-- Policy for UPDATE
CREATE POLICY "Service role can update usage" ON organization_usage
  FOR UPDATE
  USING (true)
  WITH CHECK (true); -- Assuming aggregation jobs use a service role
