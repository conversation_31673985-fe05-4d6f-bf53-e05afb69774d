CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Prompt Versions table
CREATE TABLE prompt_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  prompt_content TEXT NOT NULL,
  version_number INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL -- User who created this version
);

-- Indexes
CREATE INDEX prompt_versions_agent_id_version_idx ON prompt_versions(agent_id, version_number DESC);
CREATE INDEX prompt_versions_created_by_idx ON prompt_versions(created_by);

-- Unique Constraint
ALTER TABLE prompt_versions ADD CONSTRAINT unique_agent_version UNIQUE (agent_id, version_number);

-- RLS Policies
ALTER TABLE prompt_versions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read prompt versions for their org agents" ON prompt_versions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM agents a
      JOIN organization_members om ON a.organization_id = om.organization_id
      WHERE a.id = prompt_versions.agent_id AND om.user_id = auth.uid()
    )
  );

CREATE POLICY "Org owners/admins can create prompt versions" ON prompt_versions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM agents a
      JOIN organization_members om ON a.organization_id = om.organization_id
      WHERE a.id = prompt_versions.agent_id AND om.user_id = auth.uid() AND om.role IN ('owner', 'admin')
    )
  );
