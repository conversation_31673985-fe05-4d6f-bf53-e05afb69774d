-- Create profiles table to store user profile data with role-based access
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT NOT NULL CHECK (role IN ('free', 'pro', 'enterprise')) DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policies for profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can read their own profile" ON profiles;
CREATE POLICY "Users can read their own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);


-- Trigger to update profile when user updates their email
CREATE OR REPLACE FUNCTION public.update_profile_email()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.profiles
  SET email = NEW.email, updated_at = NOW()
  WHERE id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE TRIGGER update_profile_email_trigger
AFTER UPDATE OF email ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.update_profile_email();

-- API Keys table for organization-level authentication
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  key_prefix VARCHAR(8) NOT NULL,
  key_hash TEXT NOT NULL,
  scopes TEXT[] NOT NULL DEFAULT array['read'],
  created_by UUID NOT NULL REFERENCES auth.users(id),
  expires_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
DROP INDEX IF EXISTS api_keys_organization_id_idx;
CREATE INDEX api_keys_organization_id_idx ON api_keys(organization_id);
DROP INDEX IF EXISTS api_keys_key_prefix_idx;
CREATE INDEX api_keys_key_prefix_idx ON api_keys(key_prefix);
DROP INDEX IF EXISTS api_keys_expires_at_idx;
CREATE INDEX api_keys_expires_at_idx ON api_keys(expires_at);
DROP INDEX IF EXISTS api_keys_created_by_idx;
CREATE INDEX api_keys_created_by_idx ON api_keys(created_by);

-- Add unique constraint to key_prefix to prevent collisions
ALTER TABLE api_keys ADD CONSTRAINT api_keys_key_prefix_key UNIQUE (key_prefix);

COMMENT ON TABLE api_keys IS 'API keys for organization access. Only key prefixes and hashes are stored for security.';
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can read API keys for their organizations" ON api_keys;
CREATE POLICY "Users can read API keys for their organizations" ON api_keys
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = api_keys.organization_id
      AND user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );
DROP POLICY IF EXISTS "Only owners and admins can create API keys" ON api_keys;
CREATE POLICY "Only owners and admins can create API keys" ON api_keys
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = api_keys.organization_id
      AND user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );
DROP POLICY IF EXISTS "Only owners and admins can update API keys" ON api_keys;
CREATE POLICY "Only owners and admins can update API keys" ON api_keys
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = api_keys.organization_id
      AND user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );
DROP POLICY IF EXISTS "Only owners and admins can delete API keys" ON api_keys;
CREATE POLICY "Only owners and admins can delete API keys" ON api_keys
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE organization_id = api_keys.organization_id
      AND user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );
-- Function and trigger to update last_used_at
CREATE OR REPLACE FUNCTION update_api_key_last_used()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_used_at = NOW();
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER update_api_key_last_used_trigger
BEFORE UPDATE ON api_keys
FOR EACH ROW
WHEN (OLD.last_used_at IS DISTINCT FROM NEW.last_used_at)
EXECUTE FUNCTION update_api_key_last_used();

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Function to increment message counters or create new record
CREATE OR REPLACE FUNCTION increment_message_stats(
  p_agent_id UUID,
  p_channel_id TEXT,
  p_is_flagged BOOLEAN
)
RETURNS VOID AS $$
DECLARE
  v_today DATE := CURRENT_DATE;
BEGIN
  INSERT INTO message_stats (
    agent_id, date, channel_id, 
    total_messages, flagged_messages
  )
  VALUES (
    p_agent_id, v_today, p_channel_id,
    1, CASE WHEN p_is_flagged THEN 1 ELSE 0 END
  )
  ON CONFLICT (agent_id, date, channel_id)
  DO UPDATE SET
    total_messages = message_stats.total_messages + 1,
    flagged_messages = CASE 
      WHEN p_is_flagged THEN message_stats.flagged_messages + 1
      ELSE message_stats.flagged_messages
    END,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Platform-specific analytics tables and functions
-- Extends the general analytics schema with platform-specific functions
CREATE OR REPLACE FUNCTION increment_platform_message_stats(
  p_agent_id UUID,
  p_channel_id TEXT,
  p_is_flagged BOOLEAN
)
RETURNS VOID AS $$
BEGIN
  PERFORM increment_message_stats(
    p_agent_id,
    p_channel_id,
    p_is_flagged
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
CREATE OR REPLACE FUNCTION increment_discord_message_stats(
  p_discord_agent_id UUID,
  p_channel_id TEXT,
  p_is_flagged BOOLEAN
)
RETURNS VOID AS $$
BEGIN
  PERFORM increment_platform_message_stats(
    p_discord_agent_id,
    p_channel_id,
    p_is_flagged
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
-- Create a view for all agents with platform information
CREATE OR REPLACE VIEW platform_agents AS
SELECT
  agents.id,
  agents.platform_id,
  agents.external_id,
  agents.name,
  agents.api_key_id,
  api_keys.name AS api_key_name,
  api_keys.key_prefix AS api_key_prefix, -- Changed from api_keys.key to api_keys.key_prefix
  agents.organization_id,
  organizations.name AS organization_name,
  agents.activated_at,
  agents.last_active_at,
  agents.settings,
  agents.created_at,
  agents.updated_at
FROM
  agents
JOIN
  api_keys ON agents.api_key_id = api_keys.id
JOIN
  organizations ON agents.organization_id = organizations.id;
COMMENT ON VIEW platform_agents IS 'View for all platform agents with organization and API key details';
-- Create a view for discord-specific agent information
CREATE OR REPLACE VIEW discord_agents AS
SELECT
  id,
  external_id AS guild_id,
  name AS guild_name,
  api_key_id,
  api_key_name,
  api_key_prefix, -- Changed from api_key to api_key_prefix
  organization_id,
  organization_name,
  activated_at,
  last_active_at,
  settings,
  created_at,
  updated_at
FROM
  platform_agents
WHERE
  platform_id = 'discord';
COMMENT ON VIEW discord_agents IS 'View for Discord-specific agent data with organization and API key details';
-- Create a view for slack-specific agent information
CREATE OR REPLACE VIEW slack_agents AS
SELECT
  id,
  external_id AS team_id,
  name AS workspace_name,
  api_key_id,
  api_key_name,
  api_key_prefix, -- Changed from api_key to api_key_prefix
  organization_id,
  organization_name,
  activated_at,
  last_active_at,
  settings,
  created_at,
  updated_at
FROM
  platform_agents
WHERE
  platform_id = 'slack';
COMMENT ON VIEW slack_agents IS 'View for Slack-specific agent data with organization and API key details';
-- Create a view for telegram-specific agent information
CREATE OR REPLACE VIEW telegram_agents AS
SELECT
  id,
  external_id AS chat_id,
  name AS chat_name,
  api_key_id,
  api_key_name,
  api_key_prefix, -- Changed from api_key to api_key_prefix
  organization_id,
  organization_name,
  activated_at,
  last_active_at,
  settings,
  created_at,
  updated_at
FROM
  platform_agents
WHERE
  platform_id = 'telegram';
COMMENT ON VIEW telegram_agents IS 'View for Telegram-specific agent data with organization and API key details';
-- Function to verify which platform an agent belongs to
CREATE OR REPLACE FUNCTION get_agent_platform(p_agent_id UUID)
RETURNS TEXT AS $$
DECLARE
  v_platform_id TEXT;
BEGIN
  SELECT platform_id INTO v_platform_id
  FROM agents
  WHERE id = p_agent_id;
  RETURN v_platform_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
-- Function to verify if a platform ID belongs to an organization
CREATE OR REPLACE FUNCTION is_platform_in_organization(
  p_platform_id TEXT,
  p_external_id TEXT,
  p_organization_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  v_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM agents
    WHERE platform_id = p_platform_id
      AND external_id = p_external_id
      AND organization_id = p_organization_id
  ) INTO v_exists;
  RETURN v_exists;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to handle new users
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, email, full_name, avatar_url) -- Removed username as it's not in profiles table
  values (
    new.id,
    new.email, -- Use new.email instead of raw_user_meta_data
    new.raw_user_meta_data->>'full_name',
    new.raw_user_meta_data->>'avatar_url'
  );
  return new;
end;
$$ language plpgsql security definer;

-- Create a trigger to automatically create a profile when a new user signs up
drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute function public.handle_new_user();

-- Enable pgcrypto extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- RPC function to create an API key
CREATE OR REPLACE FUNCTION create_api_key(
  organization_id_input UUID,
  name_input TEXT,
  scopes_input TEXT[] DEFAULT array['read'],
  expires_at_input TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  current_user_id UUID := auth.uid();
  key_prefix_value VARCHAR(8);
  key_value TEXT;
  key_hash_value TEXT;
  api_key_record RECORD;
  new_api_key_id UUID;
  max_retries INT := 5; -- Maximum number of retries for key_prefix generation
  retry_count INT := 0;
BEGIN
  -- Check if the user is an owner or admin of the organization
  IF NOT EXISTS (
    SELECT 1 FROM organization_members om
    WHERE om.organization_id = organization_id_input
    AND om.user_id = current_user_id
    AND om.role IN ('owner', 'admin')
  ) THEN
    RAISE EXCEPTION 'User does not have permission to create API keys for this organization';
  END IF;

  -- Loop to attempt key generation with retries for key_prefix collision
  LOOP
    BEGIN
      -- Generate key prefix (first 8 chars of a new UUID)
      key_prefix_value := SUBSTRING(uuid_generate_v4()::TEXT FROM 1 FOR 8);
      
      -- Generate the full key (UUID)
      key_value := uuid_generate_v4()::TEXT;
      
      -- Hash the key value using bcrypt with pgcrypto
      -- This operation is wrapped in its own BEGIN/EXCEPTION block if specific error handling for crypt is needed
      BEGIN
        key_hash_value := crypt(key_value, gen_salt('bf'));
      EXCEPTION
        WHEN OTHERS THEN
          RAISE LOG 'Error during key hashing: %', SQLERRM;
          RAISE EXCEPTION 'Failed to generate API key hash. Please try again.';
      END;

      INSERT INTO api_keys (
        organization_id,
        name,
        key_prefix,
        key_hash,
        scopes,
        created_by,
        expires_at
      )
      VALUES (
        organization_id_input,
        name_input,
        key_prefix_value,
        key_hash_value,
        scopes_input,
        current_user_id,
        expires_at_input
      )
      RETURNING id INTO new_api_key_id;

      -- If insert is successful, exit loop
      EXIT; 
    
    EXCEPTION
      WHEN unique_violation THEN
        -- Specifically catch unique_violation, likely on key_prefix
        retry_count := retry_count + 1;
        IF retry_count >= max_retries THEN
          RAISE EXCEPTION 'Failed to generate a unique API key prefix after % retries. Please try again.', max_retries;
        END IF;
        RAISE LOG 'Unique key_prefix collision, retrying (attempt %/%). Prefix: %', retry_count, max_retries, key_prefix_value;
        -- Loop will continue for another attempt
      WHEN OTHERS THEN
        -- Handle other potential errors during insert
        RAISE LOG 'Error during API key insertion: %', SQLERRM;
        RAISE EXCEPTION 'Failed to create API key due to an unexpected error. Please try again.';
    END;
  END LOOP;

  -- Return the new API key details including the full key_value (this is the only time it's shown)
  SELECT jsonb_build_object(
    'id', ak.id,
    'name', ak.name,
    'organization_id', ak.organization_id,
    'key_prefix', ak.key_prefix,
    'full_key', key_prefix_value || '-' || key_value, -- Combine prefix and key for display
    'scopes', ak.scopes,
    'created_at', ak.created_at,
    'expires_at', ak.expires_at,
    'created_by', ak.created_by
  )
  INTO api_key_record
  FROM api_keys ak
  WHERE ak.id = new_api_key_id;

  RETURN api_key_record;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION create_api_key IS 'Creates a new API key for an organization. The full key is only returned once upon creation.';
