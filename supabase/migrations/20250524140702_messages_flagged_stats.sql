-- Create the flagged_messages table
CREATE TABLE IF NOT EXISTS flagged_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL,
    user_id  TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    agent_id UUID REFERENCES agents(id),
    channel_name TEXT NOT NULL,
    user_nickname TEXT NOT NULL,
    content TEXT NOT NULL,
    flagged BOOLEAN DEFAULT FALSE,
    platform_data JSONB DEFAULT '{}'::jsonb,
    categories JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'utc')
);

-- Create the message_stats table
CREATE TABLE IF NOT EXISTS message_stats (
    id SERIAL PRIMARY KEY,
    agent_id UUID REFERENCES agents(id),
    date DATE NOT NULL,
    channel_id TEXT NOT NULL,
    total_messages INTEGER DEFAULT 0,
    flagged_messages INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'utc'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'utc')
);

-- Add unique constraint for upsert behavior
ALTER TABLE message_stats
ADD CONSTRAINT unique_agent_date_channel UNIQUE (agent_id, date, channel_id);
