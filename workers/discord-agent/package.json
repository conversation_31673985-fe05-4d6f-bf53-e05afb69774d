{"name": "discord-agent", "version": "1.0.0", "type": "module", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest run --environment happy-dom", "test:watch": "vitest --environment happy-dom", "test:coverage": "vitest run --coverage --environment happy-dom", "build": "wrangler build"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240515.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^1.0.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "vitest": "^1.0.0", "wrangler": "^3.39.0"}, "dependencies": {"discord-interactions": "^4.3.0"}}