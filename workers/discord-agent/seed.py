import requests
import json
import random

# Define the URL and headers for the POST request
url = "http://localhost:8787/"
headers = {
    "Content-Type": "application/json"
}

# Define a list of possible messages to randomize from
possible_messages = [
    "Hello, World!",
    "Goodbye, World!",
    "How are you?",
    "Random message 1",
    "Random message 2",
    "Random message 3"
]

# Number of requests to send
num_requests = 5

for _ in range(num_requests):
    # Define the JSON payload with a randomized message
    payload = {
        "t": "MESSAGE_CREATE",
        "s": 123456789,
        "op": 0,
        "d": {
            "id": f"123456789012345678{random.randint(1, 100)}",  # Randomize the ID to ensure it's unique for each request
            "channel_id": "123456789012345678",
            "author": {
                "id": "123456789012345678",
                "username": "TestUser",
                "bot": False
            },
            "guild_id": "123456789012345678",  # Keeping the guild_id constant as requested
            "content": random.choice(possible_messages),  # Randomize the message content from possible_messages list
            "timestamp": f"2025-05-24T{random.randint(0, 23):02d}:{random.randint(0, 59):02d}:{random.randint(0, 59):02d}.789Z"  # Randomize the timestamp
        }
    }

    # Perform the POST request
    response = requests.post(url, headers=headers, data=json.dumps(payload))

    # Print the response from the server
    print(f"Status Code: {response.status_code}")
    print("Response Body:")
    print(response.json())
