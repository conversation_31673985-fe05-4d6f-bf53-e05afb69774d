import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import worker from '../src/index';

// Mock environment
const mockEnv = {
  DISCORD_PUBLIC_KEY: 'mock-public-key',
  DISCORD_BOT_TOKEN: 'mock-bot-token',
  OPENAI_API_KEY: 'mock-openai-key',
  COMMUZA_API_BASE: 'https://api.example.com',
  AGENT_CONFIG_KV: {
    get: vi.fn(),
    put: vi.fn(),
  },
  COMMUZA_DB_URL: 'https://db.example.com',
  COMMUZA_DB_KEY: 'mock-db-key',
};

// Mock execution context
const mockCtx = {
  waitUntil: vi.fn(),
};

// Mock fetch
global.fetch = vi.fn();

// Helper to build a request
function buildStatusRequest(guildId = '123456789') {
  const interaction = {
    type: 2, // INTERACTION
    guild_id: guildId,
    data: {
      name: 'status',
    },
  };

  return new Request('https://discord.com/api/interactions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Signature-Ed25519': 'mock-signature',
      'X-Signature-Timestamp': 'mock-timestamp',
    },
    body: JSON.stringify(interaction),
  });
}

// Mock verifyKey from discord-interactions
vi.mock('discord-interactions', () => ({
  verifyKey: vi.fn().mockResolvedValue(true),
}));

describe('API Key Validation Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should handle valid API key correctly', async () => {
    // Mock KV to return a config with an API key
    mockEnv.AGENT_CONFIG_KV.get.mockResolvedValue(JSON.stringify({
      activated: true,
      commuzaApiKey: 'cmz_di_1234_validkey',
    }));

    // Mock fetch to return a valid API key response
    const mockApiKeyResponse = {
      valid: true,
      tier: 'premium',
      quota: 50000,
      message: 'Using API key: Test Key',
      key_expires_at: '2025-01-01T00:00:00Z',
      days_until_expiry: 180,
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiKeyResponse,
    });

    const response = await worker.fetch(buildStatusRequest(), mockEnv, mockCtx);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.data.content).toContain('✅ Using a valid Commuza application key');
    expect(responseBody.data.content).toContain('Monthly quota: **50,000** messages');
    expect(responseBody.data.content).toContain('Tier: **premium**');
    expect(responseBody.data.content).toContain('API key expires:');
  });

  it('should handle expired API key correctly', async () => {
    // Mock KV to return a config with an API key
    mockEnv.AGENT_CONFIG_KV.get.mockResolvedValue(JSON.stringify({
      activated: true,
      commuzaApiKey: 'cmz_di_1234_expiredkey',
    }));

    // Mock fetch to return an expired API key response
    const mockApiKeyResponse = {
      valid: false,
      message: 'API key has expired',
      key_expires_at: '2023-01-01T00:00:00Z',
      days_until_expiry: -180,
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiKeyResponse,
    });

    const response = await worker.fetch(buildStatusRequest(), mockEnv, mockCtx);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.data.content).toContain('⚠️ Your Commuza application key appears to be invalid or expired');
    expect(responseBody.data.content).toContain('API key has expired');
  });

  it('should handle soon-to-expire API key correctly', async () => {
    // Mock KV to return a config with an API key
    mockEnv.AGENT_CONFIG_KV.get.mockResolvedValue(JSON.stringify({
      activated: true,
      commuzaApiKey: 'cmz_di_1234_expiringkey',
    }));

    // Mock fetch to return a soon-to-expire API key response
    const mockApiKeyResponse = {
      valid: true,
      tier: 'standard',
      quota: 10000,
      message: 'Using API key: Expiring Key',
      key_expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
      days_until_expiry: 3,
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiKeyResponse,
    });

    const response = await worker.fetch(buildStatusRequest(), mockEnv, mockCtx);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.data.content).toContain('✅ Using a valid Commuza application key');
    expect(responseBody.data.content).toContain('⚠️ **API key expires soon**');
    expect(responseBody.data.content).toContain('3 days remaining');
  });

  it('should handle invalid API key correctly', async () => {
    // Mock KV to return a config with an API key
    mockEnv.AGENT_CONFIG_KV.get.mockResolvedValue(JSON.stringify({
      activated: true,
      commuzaApiKey: 'cmz_di_1234_invalidkey',
    }));

    // Mock fetch to return an invalid API key response
    const mockApiKeyResponse = {
      valid: false,
      message: 'Invalid API key',
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: false,
      status: 401,
      json: async () => mockApiKeyResponse,
    });

    const response = await worker.fetch(buildStatusRequest(), mockEnv, mockCtx);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.data.content).toContain('⚠️ Your Commuza application key appears to be invalid or expired');
  });
});
