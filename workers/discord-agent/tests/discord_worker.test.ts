// tests/discord_worker.test.ts
// ============================================================================
//  Vitest suite for Commuza Discord Worker
//
//  Run:  npx vitest run --environment happy-dom
// ============================================================================
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import worker from '../src/index';

// -----------------------------------------------------------------------------
// Mock K<PERSON>space
// -----------------------------------------------------------------------------
class MemoryKV {
  store = new Map<string, string>();
  async get(k: string): Promise<string | null> { return this.store.get(k) ?? null; }
  async put(k: string, v: string): Promise<void> { this.store.set(k, v); }
  // Helper to clear the store for test isolation
  clear(): void {
    this.store.clear();
  }
}

type HeadersObject = Record<string, string>;

function buildReq(bodyObj: any, headers: HeadersObject = {}): Request {
  const body = typeof bodyObj === 'string' ? bodyObj : JSON.stringify(bodyObj);
  return new Request('https://example.com', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Signature-Ed25519': 'stub-signature', // Using a more descriptive stub
      'X-Signature-Timestamp': Date.now().toString(),
      ...headers,
    },
    body,
  });
}

interface InteractionOption {
  value: string;
}

// Interaction payload builders
function interactionPayload(name: string, opts: InteractionOption[] = [], guild_id = 'guild1') {
  return {
    type: 2, // APPLICATION_COMMAND
    guild_id,
    data: {
      name,
      options: opts,
    },
  };
}

// Gateway MESSAGE_CREATE event
function msgEvent(content: string, guild_id = 'guild1', channel_id = 'chan1', author_id = 'userX', bot = false) {
  return {
    id: `msg-${Date.now()}-${Math.random()}`, // Unique message ID
    guild_id,
    channel_id,
    content,
    author: { id: author_id, bot },
  };
}

// -----------------------------------------------------------------------------
// Global mocks and Test Context
// -----------------------------------------------------------------------------
const originalFetch = global.fetch;
let memoryKVInstance: MemoryKV;

// To handle promises from ctx.waitUntil
let promisesForWaitUntil: Promise<any>[];

const ctxStub = {
  waitUntil: (promise: Promise<any>) => {
    promisesForWaitUntil.push(promise);
  },
  // Helper to wait for all collected promises
  async waitForPromises() {
    const currentPromises = [...promisesForWaitUntil];
    promisesForWaitUntil.length = 0; // Clear for the next operation within the same test if needed
    await Promise.all(currentPromises);
  }
};

beforeEach(() => {
  // Reset promises array for each test
  promisesForWaitUntil = [];

  // Fresh KV instance for each test
  memoryKVInstance = new MemoryKV();

  // Add polyfill for crypto.subtle if it doesn't exist
  // This ensures compatibility with Node.js versions below 19
  if (!globalThis.crypto || !globalThis.crypto.subtle) {
    globalThis.crypto = globalThis.crypto || {};
    globalThis.crypto.subtle = globalThis.crypto.subtle || {
      importKey: async () => ({ type: 'public', algorithm: { name: 'NODE-ED25519' } }),
      verify: async () => true,
    };
  }

  // Stub crypto.subtle methods for Node.js environment (Vitest with happy-dom)
  // Ensure these are robust enough for the worker's verifyDiscord function
  vi.spyOn(globalThis.crypto.subtle, 'importKey').mockImplementation(async (format, keyData, algorithm, extractable, keyUsages) => {
    return { type: 'public', algorithm: { name: 'NODE-ED25519' } } as CryptoKey;
  });

  vi.spyOn(globalThis.crypto.subtle, 'verify').mockImplementation(async (algorithm, key, signature, data) => {
    return true;
  });

  // Default fetch mock for all tests, can be overridden within specific test blocks
  global.fetch = vi.fn(async (url: string | URL, init?: RequestInit) => {
    const u = url.toString();

    // Default Mock Commuza API key validation endpoint
    if (u.includes('/api/verify-key')) {
      return new Response(JSON.stringify({
        valid: true,
        tier: 'standard',
        quota: 10000
      }), { status: 200, headers: { 'Content-Type': 'application/json' } });
    }

    // Default Mock OpenAI moderation endpoint
    if (u.includes('openai.com/v1/moderations')) {
      return new Response(JSON.stringify({
        results: [{
          flagged: false,
          categories: {}
        }]
      }), { status: 200, headers: { 'Content-Type': 'application/json' } });
    }
    
    // Default Mock Discord API endpoint for posting messages
    if (u.includes('discord.com/api/v10/channels/')) {
        return new Response(JSON.stringify({ id: 'mockMessageId' }), { status: 200, headers: { 'Content-Type': 'application/json' } });
    }

    // Fallback for any other unmocked fetch calls
    console.warn(`Unhandled fetch mock for URL: ${u}`);
    return new Response(JSON.stringify({ error: 'Unhandled fetch mock' }), { status: 404, headers: { 'Content-Type': 'application/json' } });
  }) as unknown as typeof fetch;
});

afterEach(() => {
  vi.restoreAllMocks(); // Restores original implementations
  global.fetch = originalFetch; // Ensure fetch is restored
  memoryKVInstance.clear(); // Clear KV store
});

const baseEnv = {
  DISCORD_PUBLIC_KEY: 'stubpub',
  DISCORD_BOT_TOKEN: 'botToken',
  OPENAI_API_KEY: 'openai-global-key',
  COMMUZA_API_BASE: 'https://api.commuza.io'
};

// Use the shared memoryKVInstance for envWithKV
function envWithKV() {
  return { ...baseEnv, AGENT_CONFIG_KV: memoryKVInstance };
}

// -----------------------------------------------------------------------------
//  Tests
// -----------------------------------------------------------------------------

describe('Worker basic routing', () => {
  it('rejects non‑POST', async () => {
    const res = await worker.fetch(new Request('https://x', { method: 'GET' }), envWithKV(), ctxStub);
    expect(res.status).toBe(405);
  });

  it('accepts valid POST requests (PING)', async () => {
    const req = buildReq({ type: 1 }); // Discord PING interaction
    const res = await worker.fetch(req, envWithKV(), ctxStub);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.type).toBe(1); // PONG response
  });
});

describe('/activate & /status commands', () => {
  it('returns error when application key is missing for /activate', async () => {
    const req = buildReq(interactionPayload('activate', []));
    const res = await worker.fetch(req, envWithKV(), ctxStub);
    expect(res.status).toBe(200); // Discord expects 200 for interaction responses
    const body = await res.json();
    expect(body.type).toBe(4); // CHANNEL_MESSAGE_WITH_SOURCE
    expect(body.data.content).toMatch(/Please provide your Commuza application key/i);
  });

  it('validates Commuza application key with the API (mocked as invalid)', async () => {
    const env = envWithKV();
    const fetchMock = vi.mocked(global.fetch);

    fetchMock.mockImplementation(async (url: string | URL, init?: RequestInit) => {
      if (url.toString().includes('/api/verify-key')) {
        return new Response(JSON.stringify({ valid: false, message: "Test: Invalid API key" }), { status: 401, headers: { 'Content-Type': 'application/json' } });
      }
      return new Response(JSON.stringify({error: "Unexpected API call in this test"}), { status: 500 });
    });

    const req = buildReq(interactionPayload('activate', [{ value: 'invalid-commuza-key' }]));
    const res = await worker.fetch(req, env, ctxStub);
    const body = await res.json();

    expect(fetchMock).toHaveBeenCalledWith(
      `${env.COMMUZA_API_BASE}/api/verify-key`,
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Authorization': 'Bearer invalid-commuza-key'
        })
      })
    );
    expect(body.data.content).toMatch(/Test: Invalid API key/i);
  });

  it('stores valid Commuza application key and shows tier/quota info on /activate', async () => {
    const env = envWithKV();
    const commuzaKey = 'valid-commuza-key-12345';
    const fetchMock = vi.mocked(global.fetch);

    fetchMock.mockImplementation(async (url: string | URL, init?: RequestInit) => {
      if (url.toString().includes('/api/verify-key')) {
        return new Response(JSON.stringify({
          valid: true,
          tier: 'premium',
          quota: 50000
        }), { status: 200, headers: { 'Content-Type': 'application/json' } });
      }
      return new Response(JSON.stringify({error: "Unexpected API call"}), { status: 500 });
    });

    const reqActivate = buildReq(interactionPayload('activate', [{ value: commuzaKey }]));
    const activateRes = await worker.fetch(reqActivate, env, ctxStub);
    const activateBody = await activateRes.json();

    expect(activateBody.data.content).toMatch(/Moderation activated/i);
    expect(activateBody.data.content).toMatch(/Tier: \*\*premium\*\*/);
    expect(activateBody.data.content).toMatch(/Monthly quota: \*\*50,000\*\*/);

    const keyJson = await env.AGENT_CONFIG_KV.get(`discord-agent-${interactionPayload('activate').guild_id}`);
    const storedConfig = JSON.parse(keyJson || '{}');
    expect(storedConfig.activated).toBe(true);
    expect(storedConfig.commuzaApiKey).toBe(commuzaKey);

    const reqStatus = buildReq(interactionPayload('status'));
    const statusRes = await worker.fetch(reqStatus, env, ctxStub);
    const statusBody = await statusRes.json();
    expect(statusBody.data.content).toMatch(/ℹ️ Moderation status: Active./);
    expect(statusBody.data.content).toMatch(/✅ Using a valid Commuza application key./);
    expect(statusBody.data.content).toMatch(/📊 Monthly quota: \*\*50,000\*\* messages/);
    expect(statusBody.data.content).toMatch(/🔑 Tier: \*\*premium\*\*/);
    expect(statusBody.data.content).toMatch(/Messages are being scanned for harmful content/);
  });

  it('shows warning on /status when Commuza application key is no longer valid', async () => {
    const env = envWithKV();
    await env.AGENT_CONFIG_KV.put(`discord-agent-${interactionPayload('status').guild_id}`,
      JSON.stringify({ activated: true, commuzaApiKey: 'expired-key' }));

    const fetchMock = vi.mocked(global.fetch);
    fetchMock.mockImplementation(async (url: string | URL, init?: RequestInit) => {
      if (url.toString().includes('/api/verify-key')) {
        return new Response(JSON.stringify({ valid: false, message: "Key has expired." }), { status: 401, headers: { 'Content-Type': 'application/json' } });
      }
      return new Response(JSON.stringify({error: "Unexpected API call"}), { status: 500 });
    });

    const req = buildReq(interactionPayload('status'));
    const res = await worker.fetch(req, env, ctxStub);
    const body = await res.json();
    expect(body.data.content).toMatch(/ℹ️ Moderation status: Active./);
    expect(body.data.content).toMatch(/⚠️ Your Commuza application key appears to be invalid or expired./);
    expect(body.data.content).toMatch(/Reason: Key has expired./);
  });

  it('shows warning on /status when no Commuza application key is found in KV (but activated is true)', async () => {
    const env = envWithKV();
    await env.AGENT_CONFIG_KV.put(`discord-agent-${interactionPayload('status').guild_id}`,
      JSON.stringify({ activated: true }));

    const fetchMock = vi.mocked(global.fetch);

    const req = buildReq(interactionPayload('status'));
    const res = await worker.fetch(req, env, ctxStub);
    const body = await res.json();

    expect(fetchMock).not.toHaveBeenCalledWith(expect.stringContaining('/api/verify-key'), expect.anything());
    expect(body.data.content).toMatch(/⚠️ Moderation is active, but no Commuza application key is currently stored/i);
    expect(body.data.content).toMatch(/Please re-activate using/i);
  });
});

describe('Message moderation', () => {
  let fetchCalls: Array<{url: string, init?: RequestInit}>;

  beforeEach(() => {
    fetchCalls = [];
    (worker as any).resetCaches?.() || console.warn('Worker has no resetCaches method - tests may have unexpected behavior');
    global.fetch = vi.fn(async (url: string | URL, init?: RequestInit) => {
      const u = url.toString();
      fetchCalls.push({ url: u, init });

      if (u.includes('/api/verify-key')) {
        return new Response(JSON.stringify({ valid: true, tier: 'test', quota: 100 }), { status: 200, headers: { 'Content-Type': 'application/json' } });
      }
      if (u.includes('openai.com/v1/moderations')) {
        return new Response(JSON.stringify({ results: [{ flagged: false, categories: {} }] }), { status: 200, headers: { 'Content-Type': 'application/json' } });
      }
      if (u.includes('discord.com/api/v10/channels/')) {
        return new Response(JSON.stringify({ id: 'discordMsgId' }), { status: 200, headers: { 'Content-Type': 'application/json' } });
      }
      console.warn(`Unhandled fetch in 'Message moderation' suite: ${u}`);
      return new Response(JSON.stringify({error: "Unhandled fetch in Message moderation suite"}), { status: 404 });
    }) as unknown as typeof fetch;
  });

  it('verifies Commuza application key before processing messages', async () => {
    const env = envWithKV();
    const validKey = 'valid-commuza-key-for-moderation';
    await env.AGENT_CONFIG_KV.put('discord-agent-guild1',
      JSON.stringify({ activated: true, commuzaApiKey: validKey }));

    const req = buildReq(msgEvent('hello world', 'guild1'));
    await worker.fetch(req, env, ctxStub);
    await ctxStub.waitForPromises();

    const validationCall = fetchCalls.find(call => call.url.includes('/api/verify-key'));
    expect(validationCall).toBeDefined();
    expect(validationCall?.init?.headers).toHaveProperty('Authorization', `Bearer ${validKey}`);

    const moderationCall = fetchCalls.find(call => call.url.includes('openai.com'));
    expect(moderationCall).toBeDefined();
    expect(moderationCall?.init?.headers).toHaveProperty('Authorization', `Bearer ${env.OPENAI_API_KEY}`);
  });

  // it('skips OpenAI call if Commuza application key is invalid', async () => {
  //   const env = envWithKV();
  //   const invalidKey = 'invalid-key-for-moderation';
  //   await env.AGENT_CONFIG_KV.put('discord-agent-guild1',
  //     JSON.stringify({ activated: true, commuzaApiKey: invalidKey }));

  //   vi.mocked(global.fetch).mockImplementationOnce(async (url: string | URL, init?: RequestInit) => {
  //     const u = url.toString();
  //     fetchCalls.push({ url: u, init });
  //     if (u.includes('/api/verify-key')) {
  //       return new Response(JSON.stringify({ valid: false, message: "Key is super invalid" }), 
  //         { status: 401, headers: { 'Content-Type': 'application/json' } });
  //     }
  //     return new Response(JSON.stringify({ error: "Unexpected fetch in skip OpenAI test" }), { status: 404 });
  //   });

  //   const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

  //   const req = buildReq(msgEvent('hello world', 'guild1'));
  //   await worker.fetch(req, env, ctxStub);
  //   await ctxStub.waitForPromises();

  //   const validationCall = fetchCalls.find(call => call.url.includes('/api/verify-key'));
  //   expect(validationCall).toBeDefined();

  //   const moderationCall = fetchCalls.find(call => call.url.includes('openai.com'));
  //   expect(moderationCall).toBeUndefined();

  //   expect(warnSpy).toHaveBeenCalledWith(expect.stringContaining(`Invalid Commuza API key for guild guild1`));
  //   warnSpy.mockRestore();
  // });

  // it('flags inappropriate content and sends Discord alert', async () => {
  //   const env = envWithKV();
  //   const validKey = 'valid-key-for-flagging';
  //   const guildId = 'guild1';
  //   const channelId = 'chan1';

  //   await env.AGENT_CONFIG_KV.put(`discord-agent-${guildId}`,
  //     JSON.stringify({ activated: true, commuzaApiKey: validKey }));

  //   const fetchMock = vi.mocked(global.fetch);
    
  //   fetchMock.mockImplementationOnce(async (url: string | URL, init?: RequestInit) => {
  //     const u = url.toString();
  //     fetchCalls.push({ url: u, init });
  //     return new Response(JSON.stringify({ valid: true }), 
  //       { status: 200, headers: { 'Content-Type': 'application/json' } });
  //   });
    
  //   fetchMock.mockImplementationOnce(async (url: string | URL, init?: RequestInit) => {
  //     const u = url.toString();
  //     fetchCalls.push({ url: u, init });
  //     return new Response(JSON.stringify({
  //       results: [{
  //         flagged: true,
  //         categories: { 'hate': true, 'self-harm': true }
  //       }]
  //     }), { status: 200, headers: { 'Content-Type': 'application/json' } });
  //   });
    
  //   fetchMock.mockImplementationOnce(async (url: string | URL, init?: RequestInit) => {
  //     const u = url.toString();
  //     fetchCalls.push({ url: u, init });
  //     return new Response(JSON.stringify({ id: 'postedMessageId' }), 
  //       { status: 200, headers: { 'Content-Type': 'application/json' } });
  //   });

  //   const req = buildReq(msgEvent('very bad words', guildId, channelId));
  //   await worker.fetch(req, env, ctxStub);
  //   await ctxStub.waitForPromises();

  //   const validationCall = fetchCalls.find(call => call.url.includes('/api/verify-key'));
  //   expect(validationCall).toBeDefined();

  //   const moderationCall = fetchCalls.find(call => call.url.includes('openai.com'));
  //   expect(moderationCall).toBeDefined();

  //   const discordApiCall = fetchCalls.find(call => call.url.includes(`discord.com/api/v10/channels/${channelId}/messages`));
  //   expect(discordApiCall).toBeDefined();
  //   const discordAlertBody = JSON.parse(discordApiCall?.init?.body as string);
  //   expect(discordAlertBody.content).toMatch(/Flagged message/);
  //   expect(discordAlertBody.content).toMatch(/hate, self-harm/);
  //   expect(discordApiCall?.init?.headers).toHaveProperty('Authorization', `Bot ${env.DISCORD_BOT_TOKEN}`);
  // });
});

