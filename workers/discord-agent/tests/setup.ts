// Mock crypto.subtle for Node.js environments
import { vi } from 'vitest';

// Set up global fetch mock
vi.stubGlobal('fetch', vi.fn());

// Mock crypto.subtle methods that aren't available in Node.js
Object.defineProperty(globalThis.crypto.subtle, 'importKey', {
  value: vi.fn().mockResolvedValue({} as CryptoKey),
  configurable: true,
});

Object.defineProperty(globalThis.crypto.subtle, 'verify', {
  value: vi.fn().mockResolvedValue(true),
  configurable: true,
});