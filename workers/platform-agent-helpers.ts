/**
 * Shared helper functions for platform agents
 * Used by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and other platform workers
 */

interface ApiKeyValidationResult {
  valid: boolean;
  message?: string;
  quota?: number;
  tier?: string;
  agent_id?: string;  // Generic agent ID for any platform
  organization_id?: string;
  platform_id?: string;
  external_id?: string;
}

interface PlatformConfig {
  activated: boolean;
  apiKey?: string;
  agent_id?: string;
  organization_id?: string;
  platform_id?: string;
  external_id?: string;
}

/**
 * Validates an API key with the Commuza API
 * 
 * @param apiKey The API key to validate
 * @param apiBase Base URL for the Commuza API
 * @param platformData Platform-specific data to register agent
 */
export async function validatePlatformApiKey(
  apiKey: string, 
  apiBase: string,
  service: string, 
  platformData?: Record<string, any>
): Promise<ApiKeyValidationResult> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); 
    
    // Include platform data in the request if available
    const payload: any = { service };
    if (platformData) {
      payload.platform_data = platformData;
    }
    
    const response = await fetch(`${apiBase}/api/verify-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    
    let result: ApiKeyValidationResult;
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Key validation error:', response.status, errorText);
      try {
        const errorJson = JSON.parse(errorText);
        result = {
          valid: false,
          message: errorJson.message || 'Invalid API key. Please check your key and try again.'
        };
      } catch (e) {
        result = {
          valid: false,
          message: 'Invalid API key (unable to parse error details). Please check your key and try again.'
        };
      }
    } else {
      const data = await response.json();
      result = {
        valid: !!data.valid,
        tier: data.tier,
        quota: data.quota,
        message: data.message,
        agent_id: data.agent_id,
        organization_id: data.organization_id,
        platform_id: data.platform_id,
        external_id: data.external_id
      };
    }
    
    return result;

  } catch (error: any) {
    console.error('Error during validatePlatformApiKey:', error);
    let message = 'Could not connect to Commuza API to verify your key. Please try again later.';
    if (error.name === 'AbortError') {
        message = 'API key validation timed out. Please try again later.';
    }
    
    return { valid: false, message };
  }
}

/**
 * Stores a flagged message in the database
 */
export async function storeFlaggedMessage(
  agentId: string,
  messageId: string,
  channelId: string,
  channelName: string | undefined,
  userId: string,
  userNickname: string | undefined,
  content: string,
  flagCategories: Record<string, boolean>,
  dbUrl: string,
  dbKey: string,
  platformData: Record<string, any>
): Promise<void> {
  try {
    const response = await fetch(`${dbUrl}/rest/v1/flagged_messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': dbKey,
        'Authorization': `Bearer ${dbKey}`,
        'Prefer': 'return=minimal'
      },
      body: JSON.stringify({
        agent_id: agentId,
        message_id: messageId,
        channel_id: channelId,
        channel_name: channelName || channelId, // Use ID as fallback if name not provided
        user_id: userId,
        user_nickname: userNickname || userId, // Use ID as fallback if name not provided
        content,
        flagged: true,
        categories: flagCategories,
        platform_data: platformData
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to store flagged message: ${response.status} - ${errorText}`);
    }
  } catch (error) {
    console.error('Error storing flagged message:', error);
    throw error;
  }
}

/**
 * Records message analytics
 */
export async function recordMessageAnalytics(
  agentId: string, 
  channelId: string, 
  isFlagged: boolean, 
  dbUrl: string, 
  dbKey: string
): Promise<void> {
  try {
    const response = await fetch(`${dbUrl}/rest/v1/rpc/increment_message_stats`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': dbKey,
        'Authorization': `Bearer ${dbKey}`
      },
      body: JSON.stringify({
        p_agent_id: agentId,
        p_channel_id: channelId,
        p_is_flagged: isFlagged
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to record analytics: ${response.status} - ${errorText}`);
    }
  } catch (error) {
    console.error('Error recording analytics:', error);
    throw error;
  }
}

/**
 * Performs content moderation using OpenAI
 */
export async function moderateContent(
  openaiApiKey: string, 
  input: string
): Promise<{
  flagged: boolean;
  categories: Record<string, boolean>;
}> {
  if (!input || input.trim() === '') {
    return { flagged: false, categories: {} };
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); 
    
    const res = await fetch('https://api.openai.com/v1/moderations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openaiApiKey}`,
      },
      body: JSON.stringify({ 
        input,
        model: 'text-moderation-latest'
      }),
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    
    if (!res.ok) {
      const errorText = await res.text();
      console.error(`OpenAI API error: ${res.status} - ${errorText}`);
      return { flagged: false, categories: {} };
    }
    
    const data = await res.json();
    const result = data.results?.[0];

    if (!result) {
      console.error('OpenAI Moderation: Unexpected response format, missing results[0].');
      return { flagged: false, categories: {} }; 
    }
    
    return { 
      flagged: result.flagged ?? false, 
      categories: result.categories ?? {} 
    };

  } catch (error: any) {
    console.error('Error during OpenAI moderate call:', error);
    return { flagged: false, categories: {} };
  }
}
